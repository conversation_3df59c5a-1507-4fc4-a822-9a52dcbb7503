我这里有conda环境请你在名叫play的conda环境中进行我的工作，在我data中有我的
课题：E:\aaa_garbage\paly\jianmo_test1\data\B题 疾病的预测与大数据分析.pdf
数据：
E:\aaa_garbage\paly\jianmo_test1\data\附件\附录：数据集说明.docx
E:\aaa_garbage\paly\jianmo_test1\data\附件\cirrhosis.csv
E:\aaa_garbage\paly\jianmo_test1\data\附件\heart.csv
E:\aaa_garbage\paly\jianmo_test1\data\附件\stroke.csv

请你按照以下的方案进行我的数据处理以及我的数据结果


## 快速摘要

1. **预处理** – 多重插补 (MICE) / MissForest 处理缺失；IQR + LOF 查极值；SMOTE-Tomek 解决失衡。[PMC](https://pmc.ncbi.nlm.nih.gov/articles/PMC8499698/?utm_source=chatgpt.com)[BioMed Central](https://bmcmedresmethodol.biomedcentral.com/articles/10.1186/s12874-024-02392-2?utm_source=chatgpt.com)
2. **单病预测** – Logistic 回归（可解释）与树模型（Random Forest / XGBoost）两层；全部是 scikit-learn / xgboost 标配，无深度学习。[PMC](https://pmc.ncbi.nlm.nih.gov/articles/PMC11471268/?utm_source=chatgpt.com)[Nature](https://www.nature.com/articles/s41598-021-89434-7?utm_source=chatgpt.com)
3. **共病评估** – 用 Apriori 规则＋简易贝叶斯网即可输出“两病 / 三病” 联合概率。[PMC](https://pmc.ncbi.nlm.nih.gov/articles/PMC9122731/?utm_source=chatgpt.com)[Frontiers](https://www.frontiersin.org/journals/neuroscience/articles/10.3389/fnins.2022.1043922/full?utm_source=chatgpt.com)
4. **防控建议** – 依据 AHA“Life’s Essential 8” 和 WHO 指南整理 6 条行动要点，直接写信。[www.heart.org](https://www.heart.org/en/healthy-living/healthy-lifestyle/lifes-essential-8?utm_source=chatgpt.com)[巴伦周刊](https://www.barrons.com/articles/heart-disease-prevention-58cbcb22?utm_source=chatgpt.com)

---

## Problem 1 数据预处理 & 描述统计

| 步骤                | 极简做法                                                                         | 主要引用                                                                                                                                                                                          |
| ------------------- | -------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **缺失值**    | `IterativeImputer`(MICE) 对 heart & stroke；cirrhosis 先 `MissForest`再 MICE | [PMC](https://pmc.ncbi.nlm.nih.gov/articles/PMC8499698/?utm_source=chatgpt.com)[BioMed Central](https://bmcmedresmethodol.biomedcentral.com/articles/10.1186/s12874-024-02392-2?utm_source=chatgpt.com) |
| **异常值**    | 连续：IQR±1.5× →`LocalOutlierFactor`; 分类：低频合并 “Other”              | [PubMed](https://pubmed.ncbi.nlm.nih.gov/34072034/?utm_source=chatgpt.com)                                                                                                                           |
| **编码/缩放** | One-Hot + Z-score；偏斜变量 `np.log1p`                                         | –                                                                                                                                                                                                |
| **失衡**      | `SMOTETomek()`过采样＋清噪                                                     | [Medium](https://niranjanappaji.medium.com/balancing-act-mastering-imbalanced-data-with-smote-and-tomek-link-strategies-289f39597122?utm_source=chatgpt.com)                                         |
| **统计**      | 患病率、均值±SD、χ² / t-检验；箱线图 & 相关热图                               | –                                                                                                                                                                                                |

> **交付** ：一张 `profile_report.html` + 三张箱线/热图 PNG。

---

## Problem 2 三病预测模型（无深度学习）

| 疾病             | 简易基线                           | 强化模型                                             | 评估      | 解释                   |
| ---------------- | ---------------------------------- | ---------------------------------------------------- | --------- | ---------------------- |
| **心脏病** | `LogisticRegression`(L2)         | `XGBClassifier`(n_estimators = 500, max_depth = 4) | AUROC, F1 | `shap.TreeExplainer` |
| **中风**   | `LogisticRegression`             | `RandomForestClassifier`(n = 300)                  | AUROC, F1 | `shap.TreeExplainer` |
| **肝硬化** | `RandomForestClassifier`(二分类) | `GradientBoostingSurvivalAnalysis`(90-day C-index) | C-index   | `shap.TreeExplainer` |

*全部模型用 5×2 嵌套 CV；1000× bootstrap 给 95 % CI。*

---

## Problem 3 多疾病关联 & 综合风险

1. **Apriori 规则挖掘** ：

   *输入* = 三病标签 + 高危因子；设 support ≥ 1 %、lift > 1.2，输出高置信度规则（如 `{高血压, 吸烟} → 心脏病`）。[PMC](https://pmc.ncbi.nlm.nih.gov/articles/PMC9122731/?utm_source=chatgpt.com)

1. **简易贝叶斯网络** ：

   *节点* = 三病 + 年龄/高血压/糖尿病等；Hill-Climbing 学结构后用 `pgmpy` 估 CPT，可直接算

   P(两病),  P(三病)P(\text{两病}),\;P(\text{三病})**P**(**两病**)**,**P**(**三病**)**
   [Frontiers](https://www.frontiersin.org/journals/neuroscience/articles/10.3389/fnins.2022.1043922/full?utm_source=chatgpt.com)

> **输出** ：Sankey 图 + 表格列出每个共病概率。

---

## Problem 4 给 WHO 的建议（300–400 字即可）


| 重点                    | 对应数据支持                            | 行动建议                       |
| ----------------------- | --------------------------------------- | ------------------------------ |
| 控血压、控血糖          | SHAP 排名前二特征（heart & stroke）     | 普及家庭血压计；加强糖尿病筛查 |
| 减少吸烟                | 规则 `{吸烟}→{心脏病, 中风}`lift > 2 | 提高烟税、推广戒烟门诊         |
| 增加体力活动 & 健康饮食 | AHA Life’s Essential 8                 | 城市步道＋膳食指南             |
| 早期筛查肝病            | cirrhosis RF 特征中 ALT/AST 贡献大      | 免费肝功能检测                 |
| 公共教育                | 所有模型可解释图                        | 推广风险评估小程序             |
| 数据共享                | 本建模流程                              | 建立多国电子病历联盟           |

最后请你这样，问题一作为一个文件夹给出对应的脚本，以及md文件给出对应的结果图的数据分析，问题二也是一个独立的文件夹，给出对应的脚本，以及md文件给出对应的结果图的数据分析
