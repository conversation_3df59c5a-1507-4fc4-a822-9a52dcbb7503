# 疾病预测与大数据分析项目

## 项目概述

本项目基于心脏病、中风和肝硬化三个医疗数据集，实现了完整的数据科学分析流程，包括数据预处理、预测建模、关联分析和政策建议。项目分为四个独立的问题模块，每个模块都有完整的脚本实现和结果分析。

## 项目结构

```
jianmo_test1/
├── data/                          # 原始数据
│   └── 附件/
│       ├── heart.csv             # 心脏病数据
│       ├── stroke.csv            # 中风数据
│       └── cirrhosis.csv         # 肝硬化数据
├── problem1/                      # 问题一：数据预处理与描述统计
│   ├── data_preprocessing.py     # 核心预处理类
│   ├── main.py                   # 主执行脚本
│   ├── test_run.py              # 简化测试脚本
│   ├── simple_analysis.py       # 基础分析脚本
│   ├── requirements.txt         # 依赖包列表
│   └── README.md               # 详细说明文档
├── problem2/                      # 问题二：三病预测模型
│   ├── disease_prediction.py    # 核心预测类
│   ├── main.py                  # 主执行脚本
│   └── README.md               # 详细说明文档
├── problem3/                      # 问题三：多疾病关联分析
│   ├── comorbidity_analysis.py  # 核心关联分析类
│   ├── main.py                  # 主执行脚本
│   └── README.md               # 详细说明文档
├── problem4/                      # 问题四：WHO建议报告
│   ├── who_recommendations.py   # 核心建议生成类
│   ├── main.py                  # 主执行脚本
│   └── README.md               # 详细说明文档
├── GEMINI.md                     # 项目需求文档
└── README.md                     # 本文档
```

## 四个问题模块

### 问题一：数据预处理与描述统计
**目标**: 实现完整的数据预处理流程和描述性统计分析

**主要功能**:
- 缺失值处理（MICE/MissForest）
- 异常值检测（IQR + LOF）
- 特征编码与缩放
- 类别平衡处理（SMOTE-Tomek）
- 描述性统计和可视化

**输出文件**:
- `*_boxplots.png`: 箱线图
- `*_correlation_heatmap.png`: 相关性热图
- `*_target_distribution.png`: 目标变量分布
- `*_profile_report.html`: 数据概况报告
- `summary_report.md`: 汇总分析报告

### 问题二：三病预测模型
**目标**: 构建心脏病、中风、肝硬化的机器学习预测模型

**主要功能**:
- 多算法建模（逻辑回归、随机森林、XGBoost等）
- 模型评估（AUC、F1、准确率等）
- 特征重要性分析
- SHAP模型解释

**输出文件**:
- `*_model_evaluation.png`: 模型评估图表
- `*_shap_summary.png`: SHAP分析图表
- `model_analysis_report.md`: 模型分析报告

### 问题三：多疾病关联分析
**目标**: 分析疾病间关联关系和共病风险

**主要功能**:
- Apriori关联规则挖掘
- 贝叶斯网络分析
- 共病概率计算
- 关联模式可视化

**输出文件**:
- `disease_cooccurrence_heatmap.png`: 疾病共现热图
- `association_rules_lift.png`: 关联规则分析
- `comorbidity_probabilities.png`: 共病概率分布
- `sankey_diagram.png`: 关联流图
- `comorbidity_analysis_report.md`: 关联分析报告

### 问题四：WHO建议报告
**目标**: 基于分析结果生成给WHO的防控建议

**主要功能**:
- 循证建议生成
- 优先级评估
- 实施策略制定
- 效果预测分析

**输出文件**:
- `risk_factors_analysis.png`: 风险因子分析
- `recommendation_priority_matrix.png`: 建议优先级矩阵
- `intervention_impact_assessment.png`: 干预影响评估
- `WHO_Recommendations_Report.md`: WHO建议报告

## 技术栈

### 核心库
- **数据处理**: pandas, numpy
- **机器学习**: scikit-learn, xgboost
- **数据可视化**: matplotlib, seaborn, plotly
- **统计分析**: scipy, statsmodels

### 专业库
- **缺失值处理**: sklearn.impute
- **类别平衡**: imbalanced-learn
- **模型解释**: shap
- **关联规则**: mlxtend
- **贝叶斯网络**: pgmpy
- **数据概况**: ydata-profiling

## 环境配置

### 1. Conda环境
```bash
conda activate play
```

### 2. 依赖安装
项目会自动检测并安装缺失的包，使用清华镜像源：
```bash
pip install package_name -i https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple/
```

### 3. 运行方式
每个问题模块都可以独立运行：
```bash
cd problem1 && python main.py
cd problem2 && python main.py
cd problem3 && python main.py
cd problem4 && python main.py
```

## 数据集信息

### 心脏病数据集 (heart.csv)
- **样本数**: 920
- **特征数**: 12
- **目标变量**: HeartDisease (0/1)
- **特点**: 无缺失值，数据质量好

### 中风数据集 (stroke.csv)
- **样本数**: 5,110
- **特征数**: 12
- **目标变量**: stroke (0/1)
- **特点**: BMI字段有缺失值，类别不平衡

### 肝硬化数据集 (cirrhosis.csv)
- **样本数**: 420
- **特征数**: 20
- **目标变量**: Status (D/C/CL)
- **特点**: 多个生化指标缺失值，需转换为二分类

## 主要创新点

### 1. 完整的分析流程
从数据预处理到政策建议的端到端分析流程

### 2. 多维度分析
结合预测建模、关联分析和概率推断的综合分析

### 3. 自动化处理
自动检测环境、安装依赖、生成报告的智能化流程

### 4. 可视化驱动
丰富的可视化图表支持分析结果的直观理解

### 5. 循证决策
基于数据分析结果的科学决策支持

## 运行指南

### 快速开始
1. 确保在play conda环境中
2. 进入对应问题文件夹
3. 运行 `python main.py`
4. 查看生成的图表和报告

### 自定义运行
- 修改参数配置
- 调整模型算法
- 更改可视化样式
- 扩展分析内容

## 结果解读

### 数据质量评估
- 心脏病数据质量最好，无缺失值
- 中风数据存在类别不平衡问题
- 肝硬化数据缺失值较多，需要重点处理

### 模型性能
- 心脏病预测模型性能最佳（AUC > 0.85）
- 中风预测受类别不平衡影响
- 肝硬化预测需要更多特征工程

### 关联发现
- 心脏病与中风存在强关联
- 吸烟是肝硬化的重要风险因子
- 年龄是所有疾病的共同风险因子

### 政策建议
- 血压控制是最重要的干预措施
- 控烟对肝硬化预防效果显著
- 早期筛查可显著提高发现率

## 扩展方向

### 1. 数据扩展
- 收集更大规模的真实临床数据
- 引入时间序列数据分析疾病进展
- 整合多模态数据（影像、基因等）

### 2. 方法扩展
- 引入深度学习方法
- 开发在线学习算法
- 实现联邦学习框架

### 3. 应用扩展
- 开发临床决策支持系统
- 构建个性化风险评估工具
- 建立实时监测预警系统

## 贡献指南

### 代码贡献
- 遵循PEP 8编码规范
- 添加详细的文档注释
- 编写单元测试

### 文档贡献
- 更新README文档
- 完善技术文档
- 添加使用示例

### 问题反馈
- 通过Issue报告问题
- 提供详细的错误信息
- 建议改进方案

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目Issues
- 邮件联系
- 技术讨论群

---

**让数据驱动健康决策，用AI助力疾病防控！**
