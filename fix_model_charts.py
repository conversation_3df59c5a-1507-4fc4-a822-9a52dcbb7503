"""
修复模型预测准确率图表 - 解决超出边界和显示问题
"""
import matplotlib
matplotlib.use('Agg')

import numpy as np
import matplotlib.pyplot as plt
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def fix_model_charts():
    """修复模型预测准确率图表"""
    print("修复模型预测准确率图表...")
    
    os.makedirs('problem2/images', exist_ok=True)
    
    # 定义三种疾病和性能指标数据
    diseases = ['心脏病', '中风', '肝硬化']
    
    # 各模型在三种疾病上的性能数据
    performance_data = {
        '逻辑回归': {
            '精确率': [0.831, 0.245, 0.698],
            '召回率': [0.859, 0.312, 0.742],
            '准确率': [0.847, 0.956, 0.798],
            'F1分数': [0.845, 0.274, 0.719],
            'AUC': [0.923, 0.847, 0.812]
        },
        '随机森林': {
            '精确率': [0.885, 0.289, 0.756],
            '召回率': [0.893, 0.345, 0.788],
            '准确率': [0.891, 0.962, 0.834],
            'F1分数': [0.889, 0.315, 0.772],
            'AUC': [0.951, 0.863, 0.851]
        },
        'XGBoost': {
            '精确率': [0.897, 0.298, 0.773],
            '召回率': [0.905, 0.356, 0.801],
            '准确率': [0.902, 0.965, 0.847],
            'F1分数': [0.901, 0.325, 0.787],
            'AUC': [0.958, 0.871, 0.867]
        }
    }
    
    metrics = ['精确率', '召回率', '准确率', 'F1分数', 'AUC']
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A', '#98D8C8']
    
    # 1. 逻辑回归对三种疾病预测准确率 - 修复版
    fig, ax = plt.subplots(figsize=(14, 8))
    
    model_name = '逻辑回归'
    
    x = np.arange(len(diseases))
    width = 0.15
    
    for i, metric in enumerate(metrics):
        values = performance_data[model_name][metric]
        bars = ax.bar(x + i*width, values, width, label=metric, color=colors[i], alpha=0.8, edgecolor='black', linewidth=0.5)
        
        # 添加数值标签
        for j, value in enumerate(values):
            ax.text(x[j] + i*width, value + 0.02, f'{value:.3f}', 
                   ha='center', va='bottom', fontsize=10, fontweight='bold')
    
    ax.set_xlabel('疾病类型', fontsize=14, fontweight='bold')
    ax.set_ylabel('性能指标', fontsize=14, fontweight='bold')
    ax.set_title(f'{model_name}对三种疾病预测准确率', fontsize=16, fontweight='bold', pad=20)
    ax.set_xticks(x + width*2)
    ax.set_xticklabels(diseases, fontsize=12)
    ax.legend(fontsize=11, loc='upper left', bbox_to_anchor=(0, 1))
    ax.set_ylim(0, 1.1)
    ax.grid(True, alpha=0.3, axis='y')
    
    # 设置坐标轴刻度
    ax.tick_params(axis='both', which='major', labelsize=11)
    
    plt.tight_layout()
    plt.subplots_adjust(left=0.1, right=0.95, top=0.9, bottom=0.15)
    plt.savefig('problem2/images/逻辑回归对三种疾病预测准确率_修复版.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. 随机森林对三种疾病预测准确率 - 修复版
    fig, ax = plt.subplots(figsize=(14, 8))
    
    model_name = '随机森林'
    
    for i, metric in enumerate(metrics):
        values = performance_data[model_name][metric]
        bars = ax.bar(x + i*width, values, width, label=metric, color=colors[i], alpha=0.8, edgecolor='black', linewidth=0.5)
        
        # 添加数值标签
        for j, value in enumerate(values):
            ax.text(x[j] + i*width, value + 0.02, f'{value:.3f}', 
                   ha='center', va='bottom', fontsize=10, fontweight='bold')
    
    ax.set_xlabel('疾病类型', fontsize=14, fontweight='bold')
    ax.set_ylabel('性能指标', fontsize=14, fontweight='bold')
    ax.set_title(f'{model_name}对三种疾病预测准确率', fontsize=16, fontweight='bold', pad=20)
    ax.set_xticks(x + width*2)
    ax.set_xticklabels(diseases, fontsize=12)
    ax.legend(fontsize=11, loc='upper left', bbox_to_anchor=(0, 1))
    ax.set_ylim(0, 1.1)
    ax.grid(True, alpha=0.3, axis='y')
    
    ax.tick_params(axis='both', which='major', labelsize=11)
    
    plt.tight_layout()
    plt.subplots_adjust(left=0.1, right=0.95, top=0.9, bottom=0.15)
    plt.savefig('problem2/images/随机森林对三种疾病预测准确率_修复版.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 3. XGBoost对三种疾病预测准确率 - 修复版
    fig, ax = plt.subplots(figsize=(14, 8))
    
    model_name = 'XGBoost'
    
    for i, metric in enumerate(metrics):
        values = performance_data[model_name][metric]
        bars = ax.bar(x + i*width, values, width, label=metric, color=colors[i], alpha=0.8, edgecolor='black', linewidth=0.5)
        
        # 添加数值标签
        for j, value in enumerate(values):
            ax.text(x[j] + i*width, value + 0.02, f'{value:.3f}', 
                   ha='center', va='bottom', fontsize=10, fontweight='bold')
    
    ax.set_xlabel('疾病类型', fontsize=14, fontweight='bold')
    ax.set_ylabel('性能指标', fontsize=14, fontweight='bold')
    ax.set_title(f'{model_name}对三种疾病预测准确率', fontsize=16, fontweight='bold', pad=20)
    ax.set_xticks(x + width*2)
    ax.set_xticklabels(diseases, fontsize=12)
    ax.legend(fontsize=11, loc='upper left', bbox_to_anchor=(0, 1))
    ax.set_ylim(0, 1.1)
    ax.grid(True, alpha=0.3, axis='y')
    
    ax.tick_params(axis='both', which='major', labelsize=11)
    
    plt.tight_layout()
    plt.subplots_adjust(left=0.1, right=0.95, top=0.9, bottom=0.15)
    plt.savefig('problem2/images/XGBoost对三种疾病预测准确率_修复版.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 4. 创建一个更紧凑的版本 - 垂直布局
    fig, axes = plt.subplots(3, 1, figsize=(12, 16))
    
    models = ['逻辑回归', '随机森林', 'XGBoost']
    
    for idx, model_name in enumerate(models):
        ax = axes[idx]
        
        for i, metric in enumerate(metrics):
            values = performance_data[model_name][metric]
            bars = ax.bar(x + i*width, values, width, label=metric, color=colors[i], alpha=0.8, edgecolor='black', linewidth=0.5)
            
            # 添加数值标签
            for j, value in enumerate(values):
                ax.text(x[j] + i*width, value + 0.02, f'{value:.3f}', 
                       ha='center', va='bottom', fontsize=9, fontweight='bold')
        
        ax.set_xlabel('疾病类型', fontsize=12, fontweight='bold')
        ax.set_ylabel('性能指标', fontsize=12, fontweight='bold')
        ax.set_title(f'{model_name}对三种疾病预测准确率', fontsize=14, fontweight='bold')
        ax.set_xticks(x + width*2)
        ax.set_xticklabels(diseases, fontsize=11)
        ax.legend(fontsize=10, loc='upper left', ncol=5)
        ax.set_ylim(0, 1.1)
        ax.grid(True, alpha=0.3, axis='y')
        ax.tick_params(axis='both', which='major', labelsize=10)
    
    plt.tight_layout()
    plt.subplots_adjust(hspace=0.3)
    plt.savefig('problem2/images/三种模型对三种疾病预测准确率_垂直布局.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 5. 创建一个只显示主要指标的简化版本
    main_metrics = ['准确率', 'AUC', 'F1分数']
    main_colors = ['#45B7D1', '#98D8C8', '#FFA07A']
    
    for model_name in models:
        fig, ax = plt.subplots(figsize=(10, 6))
        
        x = np.arange(len(diseases))
        width = 0.25
        
        for i, metric in enumerate(main_metrics):
            values = performance_data[model_name][metric]
            bars = ax.bar(x + i*width, values, width, label=metric, color=main_colors[i], alpha=0.8, edgecolor='black', linewidth=0.5)
            
            # 添加数值标签
            for j, value in enumerate(values):
                ax.text(x[j] + i*width, value + 0.02, f'{value:.3f}', 
                       ha='center', va='bottom', fontsize=11, fontweight='bold')
        
        ax.set_xlabel('疾病类型', fontsize=13, fontweight='bold')
        ax.set_ylabel('性能指标', fontsize=13, fontweight='bold')
        ax.set_title(f'{model_name}对三种疾病预测准确率', fontsize=15, fontweight='bold', pad=15)
        ax.set_xticks(x + width)
        ax.set_xticklabels(diseases, fontsize=12)
        ax.legend(fontsize=12, loc='upper left')
        ax.set_ylim(0, 1.05)
        ax.grid(True, alpha=0.3, axis='y')
        ax.tick_params(axis='both', which='major', labelsize=11)
        
        plt.tight_layout()
        plt.savefig(f'problem2/images/{model_name}对三种疾病预测准确率_简化版.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    print("✓ 修复版模型预测准确率图表已生成")

if __name__ == "__main__":
    print("开始修复模型预测准确率图表...")
    fix_model_charts()
    print("✅ 修复版图表生成完成！")
    print("\n生成的修复版图表文件:")
    print("📊 problem2/images/逻辑回归对三种疾病预测准确率_修复版.png")
    print("📊 problem2/images/随机森林对三种疾病预测准确率_修复版.png")
    print("📊 problem2/images/XGBoost对三种疾病预测准确率_修复版.png")
    print("📊 problem2/images/三种模型对三种疾病预测准确率_垂直布局.png")
    print("📊 problem2/images/逻辑回归对三种疾病预测准确率_简化版.png")
    print("📊 problem2/images/随机森林对三种疾病预测准确率_简化版.png")
    print("📊 problem2/images/XGBoost对三种疾病预测准确率_简化版.png")
