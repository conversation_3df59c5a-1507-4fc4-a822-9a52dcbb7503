"""
修复饼状图标签重叠问题
"""
import matplotlib
matplotlib.use('Agg')

import numpy as np
import matplotlib.pyplot as plt
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def fix_pie_chart_overlap():
    """修复饼状图标签重叠问题"""
    print("修复饼状图标签重叠问题...")
    
    os.makedirs('problem2/images', exist_ok=True)
    
    # 定义数据
    diseases = ['心脏病', '中风', '肝硬化']
    models = ['逻辑回归', '随机森林', 'XGBoost']
    
    performance_data = {
        '逻辑回归': {
            '精确率': [0.831, 0.245, 0.698],
            '召回率': [0.859, 0.312, 0.742],
            '准确率': [0.847, 0.956, 0.798],
            'F1分数': [0.845, 0.274, 0.719],
            'AUC': [0.923, 0.847, 0.812]
        },
        '随机森林': {
            '精确率': [0.885, 0.289, 0.756],
            '召回率': [0.893, 0.345, 0.788],
            '准确率': [0.891, 0.962, 0.834],
            'F1分数': [0.889, 0.315, 0.772],
            'AUC': [0.951, 0.863, 0.851]
        },
        'XGBoost': {
            '精确率': [0.897, 0.298, 0.773],
            '召回率': [0.905, 0.356, 0.801],
            '准确率': [0.902, 0.965, 0.847],
            'F1分数': [0.901, 0.325, 0.787],
            'AUC': [0.958, 0.871, 0.867]
        }
    }
    
    # 1. 修复版饼状图 - 使用外部标签避免重叠
    for model_name in models:
        fig, axes = plt.subplots(1, 3, figsize=(20, 7))
        
        for i, disease in enumerate(diseases):
            ax = axes[i]
            
            # 获取该疾病的所有指标值
            metrics = ['精确率', '召回率', '准确率', 'F1分数', 'AUC']
            values = [performance_data[model_name][metric][i] for metric in metrics]
            
            # 饼状图颜色
            colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A', '#98D8C8']
            
            # 绘制饼状图，使用外部标签
            wedges, texts, autotexts = ax.pie(values, labels=metrics, autopct='%1.3f', 
                                            colors=colors, startangle=90,
                                            pctdistance=0.85,  # 百分比标签距离
                                            labeldistance=1.1,  # 标签距离
                                            explode=(0.05, 0.05, 0.05, 0.05, 0.05))  # 分离效果
            
            # 美化文本
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')
                autotext.set_fontsize(10)
                autotext.set_bbox(dict(boxstyle='round,pad=0.3', facecolor='black', alpha=0.7))
            
            for text in texts:
                text.set_fontsize(11)
                text.set_fontweight('bold')
            
            ax.set_title(f'{disease}性能分布', fontsize=14, fontweight='bold', pad=20)
        
        plt.suptitle(f'{model_name}对三种疾病预测性能分布饼图（修复版）', fontsize=18, fontweight='bold')
        plt.tight_layout()
        plt.subplots_adjust(top=0.85)
        plt.savefig(f'problem2/images/{model_name}对三种疾病预测性能_饼图_修复版.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    # 2. 环形图版本 - 更美观的替代方案
    for model_name in models:
        fig, axes = plt.subplots(1, 3, figsize=(20, 7))
        
        for i, disease in enumerate(diseases):
            ax = axes[i]
            
            metrics = ['精确率', '召回率', '准确率', 'F1分数', 'AUC']
            values = [performance_data[model_name][metric][i] for metric in metrics]
            
            colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A', '#98D8C8']
            
            # 绘制环形图
            wedges, texts = ax.pie(values, labels=metrics, colors=colors, startangle=90,
                                  wedgeprops=dict(width=0.5),  # 环形效果
                                  labeldistance=1.2)
            
            # 在中心添加数值
            for j, (metric, value) in enumerate(zip(metrics, values)):
                angle = (wedges[j].theta1 + wedges[j].theta2) / 2
                x = 0.3 * np.cos(np.radians(angle))
                y = 0.3 * np.sin(np.radians(angle))
                ax.text(x, y, f'{value:.3f}', ha='center', va='center', 
                       fontweight='bold', fontsize=10, 
                       bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.8))
            
            # 美化标签
            for text in texts:
                text.set_fontsize(11)
                text.set_fontweight('bold')
            
            ax.set_title(f'{disease}性能分布', fontsize=14, fontweight='bold', pad=20)
        
        plt.suptitle(f'{model_name}对三种疾病预测性能环形图', fontsize=18, fontweight='bold')
        plt.tight_layout()
        plt.subplots_adjust(top=0.85)
        plt.savefig(f'problem2/images/{model_name}对三种疾病预测性能_环形图.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    # 3. 分离式饼状图 - 突出显示最佳性能
    for model_name in models:
        fig, axes = plt.subplots(1, 3, figsize=(20, 7))
        
        for i, disease in enumerate(diseases):
            ax = axes[i]
            
            metrics = ['精确率', '召回率', '准确率', 'F1分数', 'AUC']
            values = [performance_data[model_name][metric][i] for metric in metrics]
            
            colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A', '#98D8C8']
            
            # 找到最大值的索引，用于突出显示
            max_idx = np.argmax(values)
            explode = [0.1 if j == max_idx else 0 for j in range(len(values))]
            
            # 绘制分离式饼状图
            wedges, texts, autotexts = ax.pie(values, labels=metrics, autopct='%1.3f', 
                                            colors=colors, startangle=90,
                                            explode=explode,
                                            shadow=True,  # 添加阴影
                                            pctdistance=0.8,
                                            labeldistance=1.15)
            
            # 美化文本
            for autotext in autotexts:
                autotext.set_color('black')
                autotext.set_fontweight('bold')
                autotext.set_fontsize(10)
            
            for text in texts:
                text.set_fontsize(11)
                text.set_fontweight('bold')
            
            # 添加最佳性能标注
            ax.text(0, -1.4, f'最佳指标: {metrics[max_idx]} ({values[max_idx]:.3f})', 
                   ha='center', va='center', fontsize=12, fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.5', facecolor='yellow', alpha=0.7))
            
            ax.set_title(f'{disease}性能分布', fontsize=14, fontweight='bold', pad=20)
        
        plt.suptitle(f'{model_name}对三种疾病预测性能分离式饼图', fontsize=18, fontweight='bold')
        plt.tight_layout()
        plt.subplots_adjust(top=0.85)
        plt.savefig(f'problem2/images/{model_name}对三种疾病预测性能_分离式饼图.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    # 4. 垂直布局饼状图 - 避免水平拥挤
    for model_name in models:
        fig, axes = plt.subplots(3, 1, figsize=(10, 18))
        
        for i, disease in enumerate(diseases):
            ax = axes[i]
            
            metrics = ['精确率', '召回率', '准确率', 'F1分数', 'AUC']
            values = [performance_data[model_name][metric][i] for metric in metrics]
            
            colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A', '#98D8C8']
            
            # 绘制饼状图
            wedges, texts, autotexts = ax.pie(values, labels=metrics, autopct='%1.3f', 
                                            colors=colors, startangle=90,
                                            pctdistance=0.85,
                                            labeldistance=1.1)
            
            # 美化文本
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')
                autotext.set_fontsize(12)
                autotext.set_bbox(dict(boxstyle='round,pad=0.3', facecolor='black', alpha=0.8))
            
            for text in texts:
                text.set_fontsize(12)
                text.set_fontweight('bold')
            
            ax.set_title(f'{disease}性能分布', fontsize=16, fontweight='bold', pad=20)
        
        plt.suptitle(f'{model_name}对三种疾病预测性能分布（垂直布局）', fontsize=20, fontweight='bold')
        plt.tight_layout()
        plt.subplots_adjust(top=0.95, hspace=0.3)
        plt.savefig(f'problem2/images/{model_name}对三种疾病预测性能_垂直饼图.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    # 5. 带图例的饼状图 - 完全避免标签重叠
    for model_name in models:
        fig, axes = plt.subplots(1, 3, figsize=(22, 8))
        
        for i, disease in enumerate(diseases):
            ax = axes[i]
            
            metrics = ['精确率', '召回率', '准确率', 'F1分数', 'AUC']
            values = [performance_data[model_name][metric][i] for metric in metrics]
            
            colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A', '#98D8C8']
            
            # 绘制饼状图，不显示标签
            wedges, texts, autotexts = ax.pie(values, autopct='%1.3f', 
                                            colors=colors, startangle=90,
                                            pctdistance=0.8)
            
            # 美化百分比文本
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')
                autotext.set_fontsize(11)
            
            # 添加图例
            ax.legend(wedges, [f'{metric}: {value:.3f}' for metric, value in zip(metrics, values)],
                     title="性能指标", loc="center left", bbox_to_anchor=(1, 0, 0.5, 1),
                     fontsize=10)
            
            ax.set_title(f'{disease}性能分布', fontsize=14, fontweight='bold', pad=20)
        
        plt.suptitle(f'{model_name}对三种疾病预测性能分布（带图例）', fontsize=18, fontweight='bold')
        plt.tight_layout()
        plt.subplots_adjust(top=0.85)
        plt.savefig(f'problem2/images/{model_name}对三种疾病预测性能_带图例饼图.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    print("✓ 修复版饼状图已生成")

if __name__ == "__main__":
    print("开始修复饼状图标签重叠问题...")
    fix_pie_chart_overlap()
    print("✅ 修复版饼状图生成完成！")
    print("\n生成的修复版图表文件:")
    print("🥧 修复版饼图: *_饼图_修复版.png")
    print("⭕ 环形图: *_环形图.png")
    print("💥 分离式饼图: *_分离式饼图.png")
    print("📊 垂直饼图: *_垂直饼图.png")
    print("📋 带图例饼图: *_带图例饼图.png")
