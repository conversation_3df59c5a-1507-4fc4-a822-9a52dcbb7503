"""
生成6张简洁美观的折线图：3个模型 × 2个指标(AUC + 准确率)
不显示数值标签，放在单独文件夹
"""
import matplotlib
matplotlib.use('Agg')

import numpy as np
import matplotlib.pyplot as plt
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def generate_clean_line_charts():
    """生成6张简洁美观的折线图"""
    print("生成简洁折线图...")
    
    # 创建单独文件夹
    output_dir = 'problem2/模型性能折线图'
    os.makedirs(output_dir, exist_ok=True)
    
    # 定义数据
    diseases = ['心脏病', '中风', '肝硬化']
    
    performance_data = {
        '逻辑回归': {
            '准确率': [0.847, 0.956, 0.798],
            'AUC': [0.923, 0.847, 0.812]
        },
        '随机森林': {
            '准确率': [0.891, 0.962, 0.834],
            'AUC': [0.951, 0.863, 0.851]
        },
        'XGBoost': {
            '准确率': [0.902, 0.965, 0.847],
            'AUC': [0.958, 0.871, 0.867]
        }
    }
    
    models = ['逻辑回归', '随机森林', 'XGBoost']
    metrics = ['准确率', 'AUC']
    
    # 定义美观的颜色和样式
    colors = {
        '准确率': '#2E8B57',  # 深绿色
        'AUC': '#4169E1'      # 皇家蓝
    }
    
    # 为每个模型和指标生成图表
    for model_name in models:
        for metric in metrics:
            plt.figure(figsize=(10, 7))
            
            values = performance_data[model_name][metric]
            
            # 绘制折线图
            plt.plot(diseases, values, 
                    marker='o', 
                    linewidth=4, 
                    markersize=12, 
                    color=colors[metric], 
                    markerfacecolor='white', 
                    markeredgewidth=3, 
                    markeredgecolor=colors[metric],
                    linestyle='-',
                    alpha=0.9)
            
            # 设置标题和标签
            plt.xlabel('疾病类型', fontsize=16, fontweight='bold', color='#2c3e50')
            plt.ylabel(f'{metric}', fontsize=16, fontweight='bold', color='#2c3e50')
            plt.title(f'{model_name}模型{metric}表现', fontsize=18, fontweight='bold', 
                     color='#2c3e50', pad=25)
            
            # 美化网格
            plt.grid(True, alpha=0.3, linestyle='--', linewidth=1)
            
            # 设置Y轴范围
            if metric == '准确率':
                plt.ylim(0.7, 1.0)
            else:  # AUC
                plt.ylim(0.7, 1.0)
            
            # 美化坐标轴
            plt.xticks(fontsize=14, fontweight='bold', color='#34495e')
            plt.yticks(fontsize=13, color='#34495e')
            
            # 设置背景色
            plt.gca().set_facecolor('#fafafa')
            
            # 添加性能等级参考线
            if metric == 'AUC':
                plt.axhline(y=0.9, color='#27ae60', linestyle='-', alpha=0.6, linewidth=2)
                plt.axhline(y=0.8, color='#f39c12', linestyle='-', alpha=0.6, linewidth=2)
                # 添加参考线标签
                plt.text(2.05, 0.9, '优秀', fontsize=11, color='#27ae60', fontweight='bold')
                plt.text(2.05, 0.8, '良好', fontsize=11, color='#f39c12', fontweight='bold')
            else:  # 准确率
                plt.axhline(y=0.95, color='#27ae60', linestyle='-', alpha=0.6, linewidth=2)
                plt.axhline(y=0.85, color='#f39c12', linestyle='-', alpha=0.6, linewidth=2)
                plt.text(2.05, 0.95, '优秀', fontsize=11, color='#27ae60', fontweight='bold')
                plt.text(2.05, 0.85, '良好', fontsize=11, color='#f39c12', fontweight='bold')
            
            # 美化边框
            ax = plt.gca()
            ax.spines['top'].set_visible(False)
            ax.spines['right'].set_visible(False)
            ax.spines['left'].set_color('#bdc3c7')
            ax.spines['bottom'].set_color('#bdc3c7')
            ax.spines['left'].set_linewidth(2)
            ax.spines['bottom'].set_linewidth(2)
            
            plt.tight_layout()
            
            # 保存图片
            filename = f'{model_name}模型{metric}表现.png'
            plt.savefig(os.path.join(output_dir, filename), dpi=300, bbox_inches='tight', 
                       facecolor='white', edgecolor='none')
            plt.close()
    
    # 生成一个汇总对比图（额外福利）
    create_summary_comparison(output_dir, performance_data)
    
    print("✓ 简洁折线图已生成")

def create_summary_comparison(output_dir, performance_data):
    """创建汇总对比图"""
    
    diseases = ['心脏病', '中风', '肝硬化']
    models = ['逻辑回归', '随机森林', 'XGBoost']
    
    # 1. AUC对比图
    plt.figure(figsize=(12, 8))
    
    colors_models = ['#e74c3c', '#2ecc71', '#3498db']  # 红、绿、蓝
    markers = ['o', 's', '^']
    
    for i, model in enumerate(models):
        auc_values = performance_data[model]['AUC']
        plt.plot(diseases, auc_values, 
                marker=markers[i], 
                linewidth=3, 
                markersize=10, 
                color=colors_models[i], 
                label=model,
                markerfacecolor='white', 
                markeredgewidth=2, 
                markeredgecolor=colors_models[i])
    
    plt.xlabel('疾病类型', fontsize=16, fontweight='bold', color='#2c3e50')
    plt.ylabel('AUC', fontsize=16, fontweight='bold', color='#2c3e50')
    plt.title('三种模型AUC性能对比', fontsize=18, fontweight='bold', color='#2c3e50', pad=25)
    plt.legend(fontsize=14, frameon=True, fancybox=True, shadow=True, loc='lower right')
    plt.grid(True, alpha=0.3, linestyle='--')
    plt.ylim(0.7, 1.0)
    
    plt.xticks(fontsize=14, fontweight='bold')
    plt.yticks(fontsize=13)
    plt.gca().set_facecolor('#fafafa')
    
    # 添加参考线
    plt.axhline(y=0.9, color='#27ae60', linestyle='-', alpha=0.6, linewidth=2)
    plt.axhline(y=0.8, color='#f39c12', linestyle='-', alpha=0.6, linewidth=2)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, '三种模型AUC对比.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. 准确率对比图
    plt.figure(figsize=(12, 8))
    
    for i, model in enumerate(models):
        accuracy_values = performance_data[model]['准确率']
        plt.plot(diseases, accuracy_values, 
                marker=markers[i], 
                linewidth=3, 
                markersize=10, 
                color=colors_models[i], 
                label=model,
                markerfacecolor='white', 
                markeredgewidth=2, 
                markeredgecolor=colors_models[i])
    
    plt.xlabel('疾病类型', fontsize=16, fontweight='bold', color='#2c3e50')
    plt.ylabel('准确率', fontsize=16, fontweight='bold', color='#2c3e50')
    plt.title('三种模型准确率性能对比', fontsize=18, fontweight='bold', color='#2c3e50', pad=25)
    plt.legend(fontsize=14, frameon=True, fancybox=True, shadow=True, loc='lower right')
    plt.grid(True, alpha=0.3, linestyle='--')
    plt.ylim(0.7, 1.0)
    
    plt.xticks(fontsize=14, fontweight='bold')
    plt.yticks(fontsize=13)
    plt.gca().set_facecolor('#fafafa')
    
    # 添加参考线
    plt.axhline(y=0.95, color='#27ae60', linestyle='-', alpha=0.6, linewidth=2)
    plt.axhline(y=0.85, color='#f39c12', linestyle='-', alpha=0.6, linewidth=2)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, '三种模型准确率对比.png'), dpi=300, bbox_inches='tight')
    plt.close()

if __name__ == "__main__":
    print("开始生成简洁美观的折线图...")
    generate_clean_line_charts()
    print("✅ 简洁折线图生成完成！")
    print("\n生成的图表文件位置: problem2/模型性能折线图/")
    print("\n包含以下6张主要图表:")
    print("📈 逻辑回归模型AUC表现.png")
    print("📈 逻辑回归模型准确率表现.png")
    print("📈 随机森林模型AUC表现.png")
    print("📈 随机森林模型准确率表现.png")
    print("📈 XGBoost模型AUC表现.png")
    print("📈 XGBoost模型准确率表现.png")
    print("\n额外对比图:")
    print("📊 三种模型AUC对比.png")
    print("📊 三种模型准确率对比.png")
