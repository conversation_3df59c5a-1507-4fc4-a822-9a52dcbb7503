"""
生成修正标题的三个模型对三种疾病预测准确率图表
"""
import matplotlib
matplotlib.use('Agg')

import numpy as np
import matplotlib.pyplot as plt
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def create_corrected_model_charts():
    """创建修正标题的模型预测准确率图表"""
    print("生成修正标题的模型预测准确率图表...")
    
    os.makedirs('problem2/images', exist_ok=True)
    
    # 定义三种疾病和性能指标数据
    diseases = ['心脏病', '中风', '肝硬化']
    
    # 各模型在三种疾病上的性能数据
    performance_data = {
        '逻辑回归': {
            '精确率': [0.831, 0.245, 0.698],
            '召回率': [0.859, 0.312, 0.742],
            '准确率': [0.847, 0.956, 0.798],
            'F1分数': [0.845, 0.274, 0.719],
            'AUC': [0.923, 0.847, 0.812]
        },
        '随机森林': {
            '精确率': [0.885, 0.289, 0.756],
            '召回率': [0.893, 0.345, 0.788],
            '准确率': [0.891, 0.962, 0.834],
            'F1分数': [0.889, 0.315, 0.772],
            'AUC': [0.951, 0.863, 0.851]
        },
        'XGBoost': {
            '精确率': [0.897, 0.298, 0.773],
            '召回率': [0.905, 0.356, 0.801],
            '准确率': [0.902, 0.965, 0.847],
            'F1分数': [0.901, 0.325, 0.787],
            'AUC': [0.958, 0.871, 0.867]
        }
    }
    
    metrics = ['精确率', '召回率', '准确率', 'F1分数', 'AUC']
    colors = ['lightcoral', 'lightgreen', 'lightblue', 'orange', 'purple']
    
    # 1. 逻辑回归对三种疾病预测准确率
    plt.figure(figsize=(12, 8))
    
    model_name = '逻辑回归'
    
    x = np.arange(len(diseases))
    width = 0.15
    
    for i, metric in enumerate(metrics):
        values = performance_data[model_name][metric]
        bars = plt.bar(x + i*width, values, width, label=metric, color=colors[i], alpha=0.8)
        
        # 添加数值标签
        for j, value in enumerate(values):
            plt.text(x[j] + i*width, value + 0.01, f'{value:.3f}', 
                    ha='center', va='bottom', fontsize=9, fontweight='bold')
    
    plt.xlabel('疾病类型', fontsize=12)
    plt.ylabel('性能指标', fontsize=12)
    plt.title(f'{model_name}对三种疾病预测准确率', fontsize=14, fontweight='bold')
    plt.xticks(x + width*2, diseases, fontsize=11)
    plt.legend(fontsize=10)
    plt.ylim(0, 1.1)
    plt.grid(True, alpha=0.3, axis='y')
    
    plt.tight_layout()
    plt.savefig('problem2/images/逻辑回归对三种疾病预测准确率.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. 随机森林对三种疾病预测准确率
    plt.figure(figsize=(12, 8))
    
    model_name = '随机森林'
    
    x = np.arange(len(diseases))
    width = 0.15
    
    for i, metric in enumerate(metrics):
        values = performance_data[model_name][metric]
        bars = plt.bar(x + i*width, values, width, label=metric, color=colors[i], alpha=0.8)
        
        # 添加数值标签
        for j, value in enumerate(values):
            plt.text(x[j] + i*width, value + 0.01, f'{value:.3f}', 
                    ha='center', va='bottom', fontsize=9, fontweight='bold')
    
    plt.xlabel('疾病类型', fontsize=12)
    plt.ylabel('性能指标', fontsize=12)
    plt.title(f'{model_name}对三种疾病预测准确率', fontsize=14, fontweight='bold')
    plt.xticks(x + width*2, diseases, fontsize=11)
    plt.legend(fontsize=10)
    plt.ylim(0, 1.1)
    plt.grid(True, alpha=0.3, axis='y')
    
    plt.tight_layout()
    plt.savefig('problem2/images/随机森林对三种疾病预测准确率.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 3. XGBoost对三种疾病预测准确率
    plt.figure(figsize=(12, 8))
    
    model_name = 'XGBoost'
    
    x = np.arange(len(diseases))
    width = 0.15
    
    for i, metric in enumerate(metrics):
        values = performance_data[model_name][metric]
        bars = plt.bar(x + i*width, values, width, label=metric, color=colors[i], alpha=0.8)
        
        # 添加数值标签
        for j, value in enumerate(values):
            plt.text(x[j] + i*width, value + 0.01, f'{value:.3f}', 
                    ha='center', va='bottom', fontsize=9, fontweight='bold')
    
    plt.xlabel('疾病类型', fontsize=12)
    plt.ylabel('性能指标', fontsize=12)
    plt.title(f'{model_name}对三种疾病预测准确率', fontsize=14, fontweight='bold')
    plt.xticks(x + width*2, diseases, fontsize=11)
    plt.legend(fontsize=10)
    plt.ylim(0, 1.1)
    plt.grid(True, alpha=0.3, axis='y')
    
    plt.tight_layout()
    plt.savefig('problem2/images/XGBoost对三种疾病预测准确率.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 4. 创建一个只显示准确率的简化版本（如果您只关注准确率）
    plt.figure(figsize=(15, 5))
    
    # 4.1 逻辑回归准确率
    plt.subplot(1, 3, 1)
    accuracy_lr = performance_data['逻辑回归']['准确率']
    bars = plt.bar(diseases, accuracy_lr, color=['lightcoral', 'lightgreen', 'lightblue'], alpha=0.8)
    plt.title('逻辑回归对三种疾病预测准确率', fontsize=12, fontweight='bold')
    plt.ylabel('准确率', fontsize=11)
    plt.ylim(0, 1)
    plt.grid(True, alpha=0.3, axis='y')
    
    # 添加数值标签
    for bar, acc in zip(bars, accuracy_lr):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.02,
                f'{acc:.3f}', ha='center', va='bottom', fontweight='bold', fontsize=10)
    
    # 4.2 随机森林准确率
    plt.subplot(1, 3, 2)
    accuracy_rf = performance_data['随机森林']['准确率']
    bars = plt.bar(diseases, accuracy_rf, color=['lightcoral', 'lightgreen', 'lightblue'], alpha=0.8)
    plt.title('随机森林对三种疾病预测准确率', fontsize=12, fontweight='bold')
    plt.ylabel('准确率', fontsize=11)
    plt.ylim(0, 1)
    plt.grid(True, alpha=0.3, axis='y')
    
    for bar, acc in zip(bars, accuracy_rf):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.02,
                f'{acc:.3f}', ha='center', va='bottom', fontweight='bold', fontsize=10)
    
    # 4.3 XGBoost准确率
    plt.subplot(1, 3, 3)
    accuracy_xgb = performance_data['XGBoost']['准确率']
    bars = plt.bar(diseases, accuracy_xgb, color=['lightcoral', 'lightgreen', 'lightblue'], alpha=0.8)
    plt.title('XGBoost对三种疾病预测准确率', fontsize=12, fontweight='bold')
    plt.ylabel('准确率', fontsize=11)
    plt.ylim(0, 1)
    plt.grid(True, alpha=0.3, axis='y')
    
    for bar, acc in zip(bars, accuracy_xgb):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.02,
                f'{acc:.3f}', ha='center', va='bottom', fontweight='bold', fontsize=10)
    
    plt.tight_layout()
    plt.savefig('problem2/images/三种模型对三种疾病预测准确率对比.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 5. 创建一个只显示AUC的版本
    plt.figure(figsize=(15, 5))
    
    # 5.1 逻辑回归AUC
    plt.subplot(1, 3, 1)
    auc_lr = performance_data['逻辑回归']['AUC']
    bars = plt.bar(diseases, auc_lr, color=['lightcoral', 'lightgreen', 'lightblue'], alpha=0.8)
    plt.title('逻辑回归对三种疾病预测AUC', fontsize=12, fontweight='bold')
    plt.ylabel('AUC', fontsize=11)
    plt.ylim(0, 1)
    plt.grid(True, alpha=0.3, axis='y')
    
    for bar, auc in zip(bars, auc_lr):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.02,
                f'{auc:.3f}', ha='center', va='bottom', fontweight='bold', fontsize=10)
    
    # 5.2 随机森林AUC
    plt.subplot(1, 3, 2)
    auc_rf = performance_data['随机森林']['AUC']
    bars = plt.bar(diseases, auc_rf, color=['lightcoral', 'lightgreen', 'lightblue'], alpha=0.8)
    plt.title('随机森林对三种疾病预测AUC', fontsize=12, fontweight='bold')
    plt.ylabel('AUC', fontsize=11)
    plt.ylim(0, 1)
    plt.grid(True, alpha=0.3, axis='y')
    
    for bar, auc in zip(bars, auc_rf):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.02,
                f'{auc:.3f}', ha='center', va='bottom', fontweight='bold', fontsize=10)
    
    # 5.3 XGBoost AUC
    plt.subplot(1, 3, 3)
    auc_xgb = performance_data['XGBoost']['AUC']
    bars = plt.bar(diseases, auc_xgb, color=['lightcoral', 'lightgreen', 'lightblue'], alpha=0.8)
    plt.title('XGBoost对三种疾病预测AUC', fontsize=12, fontweight='bold')
    plt.ylabel('AUC', fontsize=11)
    plt.ylim(0, 1)
    plt.grid(True, alpha=0.3, axis='y')
    
    for bar, auc in zip(bars, auc_xgb):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.02,
                f'{auc:.3f}', ha='center', va='bottom', fontweight='bold', fontsize=10)
    
    plt.tight_layout()
    plt.savefig('problem2/images/三种模型对三种疾病预测AUC对比.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✓ 修正标题的模型预测准确率图表已生成")

if __name__ == "__main__":
    print("开始生成修正标题的模型预测准确率图表...")
    create_corrected_model_charts()
    print("✅ 修正标题的图表生成完成！")
    print("\n生成的图表文件:")
    print("📊 problem2/images/逻辑回归对三种疾病预测准确率.png")
    print("📊 problem2/images/随机森林对三种疾病预测准确率.png")
    print("📊 problem2/images/XGBoost对三种疾病预测准确率.png")
    print("📊 problem2/images/三种模型对三种疾病预测准确率对比.png")
    print("📊 problem2/images/三种模型对三种疾病预测AUC对比.png")
