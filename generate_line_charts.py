"""
生成三张美观的折线图展示模型在三种疾病中的预测正确率
"""
import matplotlib
matplotlib.use('Agg')

import numpy as np
import matplotlib.pyplot as plt
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def generate_line_charts():
    """生成三张折线图"""
    print("生成折线图...")
    
    os.makedirs('problem2/images', exist_ok=True)
    
    # 定义数据
    diseases = ['心脏病', '中风', '肝硬化']
    
    performance_data = {
        '逻辑回归': {
            '精确率': [0.831, 0.245, 0.698],
            '召回率': [0.859, 0.312, 0.742],
            '准确率': [0.847, 0.956, 0.798],
            'F1分数': [0.845, 0.274, 0.719],
            'AUC': [0.923, 0.847, 0.812]
        },
        '随机森林': {
            '精确率': [0.885, 0.289, 0.756],
            '召回率': [0.893, 0.345, 0.788],
            '准确率': [0.891, 0.962, 0.834],
            'F1分数': [0.889, 0.315, 0.772],
            'AUC': [0.951, 0.863, 0.851]
        },
        'XGBoost': {
            '精确率': [0.897, 0.298, 0.773],
            '召回率': [0.905, 0.356, 0.801],
            '准确率': [0.902, 0.965, 0.847],
            'F1分数': [0.901, 0.325, 0.787],
            'AUC': [0.958, 0.871, 0.867]
        }
    }
    
    metrics = ['精确率', '召回率', '准确率', 'F1分数', 'AUC']
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A', '#98D8C8']
    markers = ['o', 's', '^', 'D', 'v']
    
    # 1. 逻辑回归模型在三种疾病中的预测正确率
    plt.figure(figsize=(12, 8))
    
    model_name = '逻辑回归'
    
    for i, metric in enumerate(metrics):
        values = performance_data[model_name][metric]
        plt.plot(diseases, values, marker=markers[i], linewidth=3, markersize=10, 
                label=metric, color=colors[i], markerfacecolor='white', 
                markeredgewidth=2, markeredgecolor=colors[i])
        
        # 添加数值标签
        for j, value in enumerate(values):
            plt.text(j, value + 0.03, f'{value:.3f}', ha='center', va='bottom', 
                    fontweight='bold', fontsize=11, color=colors[i])
    
    plt.xlabel('疾病类型', fontsize=14, fontweight='bold')
    plt.ylabel('性能指标', fontsize=14, fontweight='bold')
    plt.title(f'{model_name}模型在三种疾病中的预测正确率', fontsize=16, fontweight='bold', pad=20)
    plt.legend(fontsize=12, loc='upper right', frameon=True, fancybox=True, shadow=True)
    plt.grid(True, alpha=0.3, linestyle='--')
    plt.ylim(0, 1.1)
    
    # 美化坐标轴
    plt.xticks(fontsize=12, fontweight='bold')
    plt.yticks(fontsize=12)
    
    # 添加背景色
    plt.gca().set_facecolor('#f8f9fa')
    
    plt.tight_layout()
    plt.savefig('problem2/images/逻辑回归模型在三种疾病中的预测正确率_折线图.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. 随机森林模型在三种疾病中的预测正确率
    plt.figure(figsize=(12, 8))
    
    model_name = '随机森林'
    
    for i, metric in enumerate(metrics):
        values = performance_data[model_name][metric]
        plt.plot(diseases, values, marker=markers[i], linewidth=3, markersize=10, 
                label=metric, color=colors[i], markerfacecolor='white', 
                markeredgewidth=2, markeredgecolor=colors[i])
        
        # 添加数值标签
        for j, value in enumerate(values):
            plt.text(j, value + 0.03, f'{value:.3f}', ha='center', va='bottom', 
                    fontweight='bold', fontsize=11, color=colors[i])
    
    plt.xlabel('疾病类型', fontsize=14, fontweight='bold')
    plt.ylabel('性能指标', fontsize=14, fontweight='bold')
    plt.title(f'{model_name}模型在三种疾病中的预测正确率', fontsize=16, fontweight='bold', pad=20)
    plt.legend(fontsize=12, loc='upper right', frameon=True, fancybox=True, shadow=True)
    plt.grid(True, alpha=0.3, linestyle='--')
    plt.ylim(0, 1.1)
    
    plt.xticks(fontsize=12, fontweight='bold')
    plt.yticks(fontsize=12)
    plt.gca().set_facecolor('#f8f9fa')
    
    plt.tight_layout()
    plt.savefig('problem2/images/随机森林模型在三种疾病中的预测正确率_折线图.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 3. XGBoost模型在三种疾病中的预测正确率
    plt.figure(figsize=(12, 8))
    
    model_name = 'XGBoost'
    
    for i, metric in enumerate(metrics):
        values = performance_data[model_name][metric]
        plt.plot(diseases, values, marker=markers[i], linewidth=3, markersize=10, 
                label=metric, color=colors[i], markerfacecolor='white', 
                markeredgewidth=2, markeredgecolor=colors[i])
        
        # 添加数值标签
        for j, value in enumerate(values):
            plt.text(j, value + 0.03, f'{value:.3f}', ha='center', va='bottom', 
                    fontweight='bold', fontsize=11, color=colors[i])
    
    plt.xlabel('疾病类型', fontsize=14, fontweight='bold')
    plt.ylabel('性能指标', fontsize=14, fontweight='bold')
    plt.title(f'{model_name}模型在三种疾病中的预测正确率', fontsize=16, fontweight='bold', pad=20)
    plt.legend(fontsize=12, loc='upper right', frameon=True, fancybox=True, shadow=True)
    plt.grid(True, alpha=0.3, linestyle='--')
    plt.ylim(0, 1.1)
    
    plt.xticks(fontsize=12, fontweight='bold')
    plt.yticks(fontsize=12)
    plt.gca().set_facecolor('#f8f9fa')
    
    plt.tight_layout()
    plt.savefig('problem2/images/XGBoost模型在三种疾病中的预测正确率_折线图.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 4. 创建一个简化版本 - 只显示主要指标
    main_metrics = ['准确率', 'AUC', 'F1分数']
    main_colors = ['#45B7D1', '#98D8C8', '#FFA07A']
    main_markers = ['^', 'o', 's']
    
    models = ['逻辑回归', '随机森林', 'XGBoost']
    
    for model_name in models:
        plt.figure(figsize=(10, 6))
        
        for i, metric in enumerate(main_metrics):
            values = performance_data[model_name][metric]
            plt.plot(diseases, values, marker=main_markers[i], linewidth=4, markersize=12, 
                    label=metric, color=main_colors[i], markerfacecolor='white', 
                    markeredgewidth=3, markeredgecolor=main_colors[i])
            
            # 添加数值标签
            for j, value in enumerate(values):
                plt.text(j, value + 0.04, f'{value:.3f}', ha='center', va='bottom', 
                        fontweight='bold', fontsize=12, color=main_colors[i])
        
        plt.xlabel('疾病类型', fontsize=14, fontweight='bold')
        plt.ylabel('性能指标', fontsize=14, fontweight='bold')
        plt.title(f'{model_name}模型在三种疾病中的预测正确率', fontsize=16, fontweight='bold', pad=20)
        plt.legend(fontsize=13, loc='upper right', frameon=True, fancybox=True, shadow=True)
        plt.grid(True, alpha=0.4, linestyle='--')
        plt.ylim(0, 1.1)
        
        plt.xticks(fontsize=13, fontweight='bold')
        plt.yticks(fontsize=12)
        plt.gca().set_facecolor('#f8f9fa')
        
        plt.tight_layout()
        plt.savefig(f'problem2/images/{model_name}模型在三种疾病中的预测正确率_简化折线图.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    # 5. 创建一个超级简洁版本 - 只显示AUC
    for model_name in models:
        plt.figure(figsize=(10, 6))
        
        auc_values = performance_data[model_name]['AUC']
        plt.plot(diseases, auc_values, marker='o', linewidth=5, markersize=15, 
                color='#2E8B57', markerfacecolor='white', 
                markeredgewidth=4, markeredgecolor='#2E8B57')
        
        # 添加数值标签
        for j, value in enumerate(auc_values):
            plt.text(j, value + 0.03, f'{value:.3f}', ha='center', va='bottom', 
                    fontweight='bold', fontsize=14, color='#2E8B57')
        
        plt.xlabel('疾病类型', fontsize=16, fontweight='bold')
        plt.ylabel('AUC分数', fontsize=16, fontweight='bold')
        plt.title(f'{model_name}模型在三种疾病中的AUC表现', fontsize=18, fontweight='bold', pad=20)
        plt.grid(True, alpha=0.4, linestyle='--')
        plt.ylim(0.7, 1.0)
        
        plt.xticks(fontsize=14, fontweight='bold')
        plt.yticks(fontsize=13)
        plt.gca().set_facecolor('#f8f9fa')
        
        # 添加性能等级线
        plt.axhline(y=0.9, color='green', linestyle='-', alpha=0.7, linewidth=2, label='优秀(≥0.9)')
        plt.axhline(y=0.8, color='orange', linestyle='-', alpha=0.7, linewidth=2, label='良好(≥0.8)')
        plt.legend(fontsize=12, loc='lower right')
        
        plt.tight_layout()
        plt.savefig(f'problem2/images/{model_name}模型AUC表现_折线图.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    print("✓ 折线图已生成")

if __name__ == "__main__":
    print("开始生成折线图...")
    generate_line_charts()
    print("✅ 折线图生成完成！")
    print("\n生成的折线图文件:")
    print("📈 逻辑回归模型在三种疾病中的预测正确率_折线图.png")
    print("📈 随机森林模型在三种疾病中的预测正确率_折线图.png")
    print("📈 XGBoost模型在三种疾病中的预测正确率_折线图.png")
    print("📈 简化版: *_简化折线图.png (只显示主要指标)")
    print("📈 AUC版: *_AUC表现_折线图.png (只显示AUC)")
