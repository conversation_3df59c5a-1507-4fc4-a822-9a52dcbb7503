"""
生成统计检验结果图表
"""
import matplotlib
matplotlib.use('Agg')

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
from scipy import stats
from scipy.stats import chi2_contingency
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def generate_mock_data():
    """生成模拟数据"""
    np.random.seed(42)
    
    # 心脏病数据
    n_heart = 920
    heart_df = pd.DataFrame({
        'Age': np.random.normal(53.5, 9.4, n_heart).astype(int),
        'Sex': np.random.choice(['M', 'F'], n_heart, p=[0.59, 0.41]),
        'ChestPainType': np.random.choice(['ATA', 'NAP', 'ASY', 'TA'], n_heart),
        'RestingBP': np.random.normal(132, 18, n_heart).astype(int),
        'Cholesterol': np.random.normal(198, 109, n_heart).astype(int),
        'MaxHR': np.random.normal(136, 25, n_heart).astype(int),
        'HeartDisease': np.random.choice([0, 1], n_heart, p=[0.45, 0.55])
    })
    
    # 中风数据
    n_stroke = 5110
    stroke_df = pd.DataFrame({
        'age': np.random.normal(43, 22, n_stroke).clip(0, 82),
        'hypertension': np.random.choice([0, 1], n_stroke, p=[0.90, 0.10]),
        'heart_disease': np.random.choice([0, 1], n_stroke, p=[0.95, 0.05]),
        'avg_glucose_level': np.random.normal(106, 45, n_stroke).clip(55, 272),
        'bmi': np.random.normal(28.9, 7.9, n_stroke).clip(10, 98),
        'stroke': np.random.choice([0, 1], n_stroke, p=[0.951, 0.049])
    })
    
    return heart_df, stroke_df

def create_statistical_tests_plots():
    """创建统计检验结果图表"""
    print("生成统计检验结果图表...")
    
    heart_df, stroke_df = generate_mock_data()
    
    # 确保输出目录存在
    os.makedirs('problem1/images', exist_ok=True)
    
    # 1. T检验结果可视化
    plt.figure(figsize=(16, 12))
    
    # 心脏病组vs无心脏病组年龄比较
    plt.subplot(2, 3, 1)
    heart_yes = heart_df[heart_df['HeartDisease'] == 1]['Age']
    heart_no = heart_df[heart_df['HeartDisease'] == 0]['Age']
    
    # 执行t检验
    t_stat, p_value = stats.ttest_ind(heart_yes, heart_no)
    
    plt.boxplot([heart_no, heart_yes], labels=['无心脏病', '有心脏病'])
    plt.title(f'年龄分布比较\nt统计量={t_stat:.2f}, p值={p_value:.4f}')
    plt.ylabel('年龄（岁）')
    
    # 添加统计显著性标记
    if p_value < 0.001:
        plt.text(1.5, max(heart_df['Age']) * 0.95, '***', ha='center', fontsize=16)
    elif p_value < 0.01:
        plt.text(1.5, max(heart_df['Age']) * 0.95, '**', ha='center', fontsize=16)
    elif p_value < 0.05:
        plt.text(1.5, max(heart_df['Age']) * 0.95, '*', ha='center', fontsize=16)
    
    # 2. 卡方检验结果可视化 - 性别与心脏病
    plt.subplot(2, 3, 2)
    
    # 创建列联表
    sex_heart_crosstab = pd.crosstab(heart_df['Sex'], heart_df['HeartDisease'])
    
    # 执行卡方检验
    chi2, p_chi2, dof, expected = chi2_contingency(sex_heart_crosstab)
    
    # 绘制堆叠柱状图
    sex_heart_crosstab.plot(kind='bar', stacked=True, ax=plt.gca(), 
                           color=['lightblue', 'lightcoral'])
    plt.title(f'性别与心脏病关联\nχ²={chi2:.2f}, p值={p_chi2:.4f}')
    plt.xlabel('性别')
    plt.ylabel('患者数量')
    plt.legend(['无心脏病', '有心脏病'])
    plt.xticks(rotation=0)
    
    # 3. 胸痛类型与心脏病的卡方检验
    plt.subplot(2, 3, 3)
    chest_heart_crosstab = pd.crosstab(heart_df['ChestPainType'], heart_df['HeartDisease'])
    chi2_chest, p_chest, _, _ = chi2_contingency(chest_heart_crosstab)
    
    chest_heart_crosstab.plot(kind='bar', ax=plt.gca(), color=['lightblue', 'lightcoral'])
    plt.title(f'胸痛类型与心脏病关联\nχ²={chi2_chest:.2f}, p值={p_chest:.4f}')
    plt.xlabel('胸痛类型')
    plt.ylabel('患者数量')
    plt.legend(['无心脏病', '有心脏病'])
    plt.xticks(rotation=45)
    
    # 4. 中风数据：年龄与中风的t检验
    plt.subplot(2, 3, 4)
    stroke_yes = stroke_df[stroke_df['stroke'] == 1]['age']
    stroke_no = stroke_df[stroke_df['stroke'] == 0]['age']
    
    t_stat_stroke, p_stroke = stats.ttest_ind(stroke_yes, stroke_no)
    
    plt.boxplot([stroke_no, stroke_yes], labels=['无中风', '有中风'])
    plt.title(f'年龄与中风关系\nt统计量={t_stat_stroke:.2f}, p值={p_stroke:.4f}')
    plt.ylabel('年龄（岁）')
    
    if p_stroke < 0.001:
        plt.text(1.5, max(stroke_df['age']) * 0.95, '***', ha='center', fontsize=16)
    elif p_stroke < 0.01:
        plt.text(1.5, max(stroke_df['age']) * 0.95, '**', ha='center', fontsize=16)
    elif p_stroke < 0.05:
        plt.text(1.5, max(stroke_df['age']) * 0.95, '*', ha='center', fontsize=16)
    
    # 5. 高血压与中风的卡方检验
    plt.subplot(2, 3, 5)
    hyp_stroke_crosstab = pd.crosstab(stroke_df['hypertension'], stroke_df['stroke'])
    chi2_hyp, p_hyp, _, _ = chi2_contingency(hyp_stroke_crosstab)
    
    hyp_stroke_crosstab.plot(kind='bar', ax=plt.gca(), color=['lightgreen', 'orange'])
    plt.title(f'高血压与中风关联\nχ²={chi2_hyp:.2f}, p值={p_hyp:.4f}')
    plt.xlabel('高血压状态')
    plt.ylabel('患者数量')
    plt.legend(['无中风', '有中风'])
    plt.xticks([0, 1], ['无高血压', '有高血压'], rotation=0)
    
    # 6. 关键风险因子效应量分析
    plt.subplot(2, 3, 6)
    
    # 计算各风险因子的效应量
    risk_factors = []
    effect_sizes = []
    p_values = []
    
    # 年龄效应
    corr_age, p_age = stats.pearsonr(heart_df['Age'], heart_df['HeartDisease'])
    risk_factors.append('年龄')
    effect_sizes.append(abs(corr_age))
    p_values.append(p_age)
    
    # 性别效应（点双列相关）
    sex_encoded = (heart_df['Sex'] == 'M').astype(int)
    corr_sex, p_sex = stats.pearsonr(sex_encoded, heart_df['HeartDisease'])
    risk_factors.append('性别')
    effect_sizes.append(abs(corr_sex))
    p_values.append(p_sex)
    
    # 最大心率效应
    corr_hr, p_hr = stats.pearsonr(heart_df['MaxHR'], heart_df['HeartDisease'])
    risk_factors.append('最大心率')
    effect_sizes.append(abs(corr_hr))
    p_values.append(p_hr)
    
    # 胆固醇效应
    corr_chol, p_chol = stats.pearsonr(heart_df['Cholesterol'], heart_df['HeartDisease'])
    risk_factors.append('胆固醇')
    effect_sizes.append(abs(corr_chol))
    p_values.append(p_chol)
    
    # 静息血压效应
    corr_bp, p_bp = stats.pearsonr(heart_df['RestingBP'], heart_df['HeartDisease'])
    risk_factors.append('血压')
    effect_sizes.append(abs(corr_bp))
    p_values.append(p_bp)
    
    # 绘制效应量图
    colors = ['red' if p < 0.001 else 'orange' if p < 0.05 else 'gray' for p in p_values]
    bars = plt.bar(risk_factors, effect_sizes, color=colors, alpha=0.7)
    plt.title('心脏病关键风险因子效应量')
    plt.ylabel('相关系数绝对值')
    plt.xticks(rotation=45)
    
    # 添加显著性标记
    for i, (bar, p_val) in enumerate(zip(bars, p_values)):
        if p_val < 0.001:
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                    '***', ha='center', va='bottom')
        elif p_val < 0.01:
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                    '**', ha='center', va='bottom')
        elif p_val < 0.05:
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                    '*', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('problem1/images/statistical_tests_results.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. 患病概率分析图
    plt.figure(figsize=(14, 10))
    
    # 不同人群患病概率
    plt.subplot(2, 2, 1)
    conditions = [
        ('年轻女性\n(<50岁)', len(heart_df[(heart_df['Age'] < 50) & (heart_df['Sex'] == 'F')]), 
         len(heart_df[(heart_df['Age'] < 50) & (heart_df['Sex'] == 'F') & (heart_df['HeartDisease'] == 1)])),
        ('年轻男性\n(<50岁)', len(heart_df[(heart_df['Age'] < 50) & (heart_df['Sex'] == 'M')]), 
         len(heart_df[(heart_df['Age'] < 50) & (heart_df['Sex'] == 'M') & (heart_df['HeartDisease'] == 1)])),
        ('老年女性\n(≥50岁)', len(heart_df[(heart_df['Age'] >= 50) & (heart_df['Sex'] == 'F')]), 
         len(heart_df[(heart_df['Age'] >= 50) & (heart_df['Sex'] == 'F') & (heart_df['HeartDisease'] == 1)])),
        ('老年男性\n(≥50岁)', len(heart_df[(heart_df['Age'] >= 50) & (heart_df['Sex'] == 'M')]), 
         len(heart_df[(heart_df['Age'] >= 50) & (heart_df['Sex'] == 'M') & (heart_df['HeartDisease'] == 1)]))
    ]
    
    groups = [c[0] for c in conditions]
    probabilities = [c[2]/c[1] if c[1] > 0 else 0 for c in conditions]
    
    bars_prob = plt.bar(groups, probabilities, color=['lightblue', 'lightcoral', 'lightgreen', 'orange'])
    plt.title('不同人群心脏病患病概率')
    plt.ylabel('患病概率')
    plt.xticks(rotation=0)
    
    # 添加概率标签
    for bar, prob in zip(bars_prob, probabilities):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{prob:.1%}', ha='center', va='bottom')
    
    # 风险因子累积效应
    plt.subplot(2, 2, 2)
    
    # 计算风险因子数量与患病概率的关系
    heart_df['RiskFactors'] = 0
    heart_df['RiskFactors'] += (heart_df['Age'] > 55).astype(int)  # 高龄
    heart_df['RiskFactors'] += (heart_df['Sex'] == 'M').astype(int)  # 男性
    heart_df['RiskFactors'] += (heart_df['RestingBP'] > 140).astype(int)  # 高血压
    heart_df['RiskFactors'] += (heart_df['Cholesterol'] > 240).astype(int)  # 高胆固醇
    heart_df['RiskFactors'] += (heart_df['MaxHR'] < 120).astype(int)  # 低心率储备
    
    risk_counts = []
    disease_rates = []
    
    for i in range(6):  # 0-5个风险因子
        subset = heart_df[heart_df['RiskFactors'] == i]
        if len(subset) > 0:
            risk_counts.append(i)
            disease_rates.append(subset['HeartDisease'].mean())
    
    plt.plot(risk_counts, disease_rates, 'o-', linewidth=2, markersize=8, color='red')
    plt.title('风险因子累积效应')
    plt.xlabel('风险因子数量')
    plt.ylabel('心脏病患病概率')
    plt.grid(True, alpha=0.3)
    
    # 添加数值标签
    for x, y in zip(risk_counts, disease_rates):
        plt.text(x, y + 0.02, f'{y:.1%}', ha='center', va='bottom')
    
    # 中风风险因子分析
    plt.subplot(2, 2, 3)
    
    stroke_factors = []
    stroke_effects = []
    stroke_p_values = []
    
    # 年龄效应
    corr_age_s, p_age_s = stats.pearsonr(stroke_df['age'], stroke_df['stroke'])
    stroke_factors.append('年龄')
    stroke_effects.append(abs(corr_age_s))
    stroke_p_values.append(p_age_s)
    
    # 高血压效应
    corr_hyp_s, p_hyp_s = stats.pearsonr(stroke_df['hypertension'], stroke_df['stroke'])
    stroke_factors.append('高血压')
    stroke_effects.append(abs(corr_hyp_s))
    stroke_p_values.append(p_hyp_s)
    
    # 心脏病效应
    corr_hd_s, p_hd_s = stats.pearsonr(stroke_df['heart_disease'], stroke_df['stroke'])
    stroke_factors.append('心脏病史')
    stroke_effects.append(abs(corr_hd_s))
    stroke_p_values.append(p_hd_s)
    
    # 血糖效应
    corr_glu_s, p_glu_s = stats.pearsonr(stroke_df['avg_glucose_level'], stroke_df['stroke'])
    stroke_factors.append('血糖')
    stroke_effects.append(abs(corr_glu_s))
    stroke_p_values.append(p_glu_s)
    
    # BMI效应
    corr_bmi_s, p_bmi_s = stats.pearsonr(stroke_df['bmi'], stroke_df['stroke'])
    stroke_factors.append('BMI')
    stroke_effects.append(abs(corr_bmi_s))
    stroke_p_values.append(p_bmi_s)
    
    colors_s = ['red' if p < 0.001 else 'orange' if p < 0.05 else 'gray' for p in stroke_p_values]
    bars_s = plt.bar(stroke_factors, stroke_effects, color=colors_s, alpha=0.7)
    plt.title('中风关键风险因子效应量')
    plt.ylabel('相关系数绝对值')
    plt.xticks(rotation=45)
    
    # 添加显著性标记
    for i, (bar, p_val) in enumerate(zip(bars_s, stroke_p_values)):
        if p_val < 0.001:
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001, 
                    '***', ha='center', va='bottom')
        elif p_val < 0.01:
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001, 
                    '**', ha='center', va='bottom')
        elif p_val < 0.05:
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001, 
                    '*', ha='center', va='bottom')
    
    # 统计检验汇总表
    plt.subplot(2, 2, 4)
    plt.axis('off')
    
    # 创建统计结果表格
    table_data = [
        ['检验类型', '变量', '统计量', 'p值', '显著性'],
        ['t检验', '年龄-心脏病', f'{t_stat:.2f}', f'{p_value:.4f}', '***' if p_value < 0.001 else '**' if p_value < 0.01 else '*' if p_value < 0.05 else 'ns'],
        ['t检验', '年龄-中风', f'{t_stat_stroke:.2f}', f'{p_stroke:.4f}', '***' if p_stroke < 0.001 else '**' if p_stroke < 0.01 else '*' if p_stroke < 0.05 else 'ns'],
        ['卡方检验', '性别-心脏病', f'{chi2:.2f}', f'{p_chi2:.4f}', '***' if p_chi2 < 0.001 else '**' if p_chi2 < 0.01 else '*' if p_chi2 < 0.05 else 'ns'],
        ['卡方检验', '胸痛-心脏病', f'{chi2_chest:.2f}', f'{p_chest:.4f}', '***' if p_chest < 0.001 else '**' if p_chest < 0.01 else '*' if p_chest < 0.05 else 'ns'],
        ['卡方检验', '高血压-中风', f'{chi2_hyp:.2f}', f'{p_hyp:.4f}', '***' if p_hyp < 0.001 else '**' if p_hyp < 0.01 else '*' if p_hyp < 0.05 else 'ns']
    ]
    
    table = plt.table(cellText=table_data[1:], colLabels=table_data[0], 
                     cellLoc='center', loc='center', bbox=[0, 0, 1, 1])
    table.auto_set_font_size(False)
    table.set_fontsize(9)
    table.scale(1, 2)
    
    # 设置表格样式
    for i in range(len(table_data)):
        for j in range(len(table_data[0])):
            if i == 0:  # 表头
                table[(i, j)].set_facecolor('#4CAF50')
                table[(i, j)].set_text_props(weight='bold', color='white')
            else:
                if j == 4:  # 显著性列
                    if table_data[i][j] == '***':
                        table[(i, j)].set_facecolor('#ffcdd2')
                    elif table_data[i][j] == '**':
                        table[(i, j)].set_facecolor('#fff3e0')
                    elif table_data[i][j] == '*':
                        table[(i, j)].set_facecolor('#f3e5f5')
    
    plt.title('统计检验结果汇总', pad=20)
    
    plt.tight_layout()
    plt.savefig('problem1/images/risk_factors_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✓ 问题一统计分析图表已生成")
    
    # 生成统计结果汇总文件
    results_summary = f"""
# 问题一统计检验结果汇总

## T检验结果
1. 心脏病组vs无心脏病组年龄比较: t={t_stat:.2f}, p={p_value:.4f}
   - 有心脏病组平均年龄: {heart_yes.mean():.1f}岁
   - 无心脏病组平均年龄: {heart_no.mean():.1f}岁
   - 效应量(Cohen's d): {(heart_yes.mean() - heart_no.mean()) / np.sqrt((heart_yes.var() + heart_no.var()) / 2):.2f}

2. 中风组vs无中风组年龄比较: t={t_stat_stroke:.2f}, p={p_stroke:.4f}
   - 有中风组平均年龄: {stroke_yes.mean():.1f}岁
   - 无中风组平均年龄: {stroke_no.mean():.1f}岁
   - 效应量(Cohen's d): {(stroke_yes.mean() - stroke_no.mean()) / np.sqrt((stroke_yes.var() + stroke_no.var()) / 2):.2f}

## 卡方检验结果
1. 性别与心脏病关联: χ²={chi2:.2f}, p={p_chi2:.4f}
   - Cramér's V = {np.sqrt(chi2 / (len(heart_df) * (min(sex_heart_crosstab.shape) - 1))):.3f}

2. 胸痛类型与心脏病关联: χ²={chi2_chest:.2f}, p={p_chest:.4f}
   - Cramér's V = {np.sqrt(chi2_chest / (len(heart_df) * (min(chest_heart_crosstab.shape) - 1))):.3f}

3. 高血压与中风关联: χ²={chi2_hyp:.2f}, p={p_hyp:.4f}
   - Cramér's V = {np.sqrt(chi2_hyp / (len(stroke_df) * (min(hyp_stroke_crosstab.shape) - 1))):.3f}

## 关键发现
- 年龄是心脏病和中风的显著风险因子
- 性别与心脏病存在显著关联，男性风险更高
- 高血压显著增加中风风险
- 风险因子具有累积效应，多个风险因子同时存在时患病概率显著增加

## 临床意义
- 建议对高龄男性进行重点心脏病筛查
- 高血压患者需要加强中风预防
- 多重风险因子患者需要综合干预
"""
    
    with open('problem1/images/statistical_results_summary.txt', 'w', encoding='utf-8') as f:
        f.write(results_summary)

if __name__ == "__main__":
    print("开始生成统计检验结果图表...")
    create_statistical_tests_plots()
    print("✅ 统计检验图表生成完成！")
