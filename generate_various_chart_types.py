"""
生成多种图表类型展示模型性能数据
"""
import matplotlib
matplotlib.use('Agg')

import numpy as np
import matplotlib.pyplot as plt
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def create_various_chart_types():
    """创建多种图表类型展示模型性能"""
    print("生成多种图表类型...")
    
    os.makedirs('problem2/images', exist_ok=True)
    
    # 定义数据
    diseases = ['心脏病', '中风', '肝硬化']
    models = ['逻辑回归', '随机森林', 'XGBoost']
    
    performance_data = {
        '逻辑回归': {
            '精确率': [0.831, 0.245, 0.698],
            '召回率': [0.859, 0.312, 0.742],
            '准确率': [0.847, 0.956, 0.798],
            'F1分数': [0.845, 0.274, 0.719],
            'AUC': [0.923, 0.847, 0.812]
        },
        '随机森林': {
            '精确率': [0.885, 0.289, 0.756],
            '召回率': [0.893, 0.345, 0.788],
            '准确率': [0.891, 0.962, 0.834],
            'F1分数': [0.889, 0.315, 0.772],
            'AUC': [0.951, 0.863, 0.851]
        },
        'XGBoost': {
            '精确率': [0.897, 0.298, 0.773],
            '召回率': [0.905, 0.356, 0.801],
            '准确率': [0.902, 0.965, 0.847],
            'F1分数': [0.901, 0.325, 0.787],
            'AUC': [0.958, 0.871, 0.867]
        }
    }
    
    # 1. 雷达图 - 展示模型在不同指标上的表现
    for model_name in models:
        fig, axes = plt.subplots(1, 3, figsize=(18, 6), subplot_kw=dict(projection='polar'))
        
        metrics = ['精确率', '召回率', '准确率', 'F1分数', 'AUC']
        
        for i, disease in enumerate(diseases):
            ax = axes[i]
            
            # 获取该疾病的所有指标值
            values = [performance_data[model_name][metric][i] for metric in metrics]
            values += values[:1]  # 闭合雷达图
            
            # 角度
            angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
            angles += angles[:1]
            
            # 绘制雷达图
            ax.plot(angles, values, 'o-', linewidth=2, label=disease, color=['red', 'green', 'blue'][i])
            ax.fill(angles, values, alpha=0.25, color=['red', 'green', 'blue'][i])
            
            # 设置标签
            ax.set_xticks(angles[:-1])
            ax.set_xticklabels(metrics, fontsize=10)
            ax.set_ylim(0, 1)
            ax.set_title(f'{disease}', fontsize=12, fontweight='bold', pad=20)
            ax.grid(True)
            
            # 添加数值标签
            for angle, value in zip(angles[:-1], values[:-1]):
                ax.text(angle, value + 0.05, f'{value:.3f}', ha='center', va='center', fontsize=9)
        
        plt.suptitle(f'{model_name}对三种疾病预测性能雷达图', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig(f'problem2/images/{model_name}对三种疾病预测性能_雷达图.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    # 2. 饼状图 - 展示各指标的相对重要性（基于平均性能）
    for model_name in models:
        fig, axes = plt.subplots(1, 3, figsize=(18, 6))
        
        for i, disease in enumerate(diseases):
            ax = axes[i]
            
            # 获取该疾病的所有指标值
            metrics = ['精确率', '召回率', '准确率', 'F1分数', 'AUC']
            values = [performance_data[model_name][metric][i] for metric in metrics]
            
            # 饼状图颜色
            colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A', '#98D8C8']
            
            # 绘制饼状图
            wedges, texts, autotexts = ax.pie(values, labels=metrics, autopct='%1.3f', 
                                            colors=colors, startangle=90)
            
            # 美化文本
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')
                autotext.set_fontsize(10)
            
            for text in texts:
                text.set_fontsize(10)
                text.set_fontweight('bold')
            
            ax.set_title(f'{disease}性能分布', fontsize=12, fontweight='bold')
        
        plt.suptitle(f'{model_name}对三种疾病预测性能分布饼图', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig(f'problem2/images/{model_name}对三种疾病预测性能_饼图.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    # 3. 热力图 - 展示所有模型和疾病的性能矩阵
    metrics = ['精确率', '召回率', '准确率', 'F1分数', 'AUC']
    
    for metric in metrics:
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # 创建数据矩阵
        data_matrix = []
        for model in models:
            row = [performance_data[model][metric][i] for i in range(len(diseases))]
            data_matrix.append(row)
        
        data_matrix = np.array(data_matrix)
        
        # 绘制热力图
        im = ax.imshow(data_matrix, cmap='RdYlGn', aspect='auto', vmin=0, vmax=1)
        
        # 设置标签
        ax.set_xticks(np.arange(len(diseases)))
        ax.set_yticks(np.arange(len(models)))
        ax.set_xticklabels(diseases, fontsize=12)
        ax.set_yticklabels(models, fontsize=12)
        
        # 添加数值标签
        for i in range(len(models)):
            for j in range(len(diseases)):
                text = ax.text(j, i, f'{data_matrix[i, j]:.3f}', 
                             ha="center", va="center", color="black", fontweight='bold', fontsize=11)
        
        ax.set_title(f'三种模型在三种疾病上的{metric}热力图', fontsize=14, fontweight='bold', pad=20)
        ax.set_xlabel('疾病类型', fontsize=12, fontweight='bold')
        ax.set_ylabel('模型类型', fontsize=12, fontweight='bold')
        
        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax)
        cbar.set_label(metric, fontsize=12, fontweight='bold')
        
        plt.tight_layout()
        plt.savefig(f'problem2/images/三种模型{metric}_热力图.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    # 4. 折线图 - 展示模型性能趋势
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.flatten()
    
    metrics = ['精确率', '召回率', '准确率', 'F1分数', 'AUC']
    colors = ['red', 'green', 'blue']
    markers = ['o', 's', '^']
    
    for idx, metric in enumerate(metrics):
        ax = axes[idx]
        
        for i, model in enumerate(models):
            values = performance_data[model][metric]
            ax.plot(diseases, values, marker=markers[i], linewidth=2, markersize=8, 
                   label=model, color=colors[i])
            
            # 添加数值标签
            for j, value in enumerate(values):
                ax.text(j, value + 0.02, f'{value:.3f}', ha='center', va='bottom', 
                       fontweight='bold', fontsize=9)
        
        ax.set_title(f'{metric}对比', fontsize=12, fontweight='bold')
        ax.set_ylabel(metric, fontsize=11)
        ax.set_xlabel('疾病类型', fontsize=11)
        ax.legend(fontsize=10)
        ax.grid(True, alpha=0.3)
        ax.set_ylim(0, 1.05)
    
    # 删除多余的子图
    axes[5].remove()
    
    plt.suptitle('三种模型在三种疾病上的性能趋势对比', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('problem2/images/三种模型性能趋势_折线图.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 5. 散点图 - 展示精确率vs召回率
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    
    for i, disease in enumerate(diseases):
        ax = axes[i]
        
        for j, model in enumerate(models):
            precision = performance_data[model]['精确率'][i]
            recall = performance_data[model]['召回率'][i]
            
            ax.scatter(precision, recall, s=200, alpha=0.7, color=colors[j], 
                      marker=markers[j], label=model, edgecolors='black', linewidth=2)
            
            # 添加模型标签
            ax.annotate(model, (precision, recall), xytext=(5, 5), 
                       textcoords='offset points', fontsize=10, fontweight='bold')
        
        ax.set_xlabel('精确率', fontsize=12, fontweight='bold')
        ax.set_ylabel('召回率', fontsize=12, fontweight='bold')
        ax.set_title(f'{disease}：精确率 vs 召回率', fontsize=12, fontweight='bold')
        ax.grid(True, alpha=0.3)
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.legend(fontsize=10)
        
        # 添加对角线（理想情况）
        ax.plot([0, 1], [0, 1], 'k--', alpha=0.5, label='理想线')
    
    plt.suptitle('三种疾病的精确率vs召回率散点图', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('problem2/images/精确率vs召回率_散点图.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 6. 堆叠条形图 - 展示各指标的贡献
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    
    for i, model in enumerate(models):
        ax = axes[i]
        
        metrics = ['精确率', '召回率', '准确率', 'F1分数', 'AUC']
        colors_stack = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A', '#98D8C8']
        
        # 准备数据
        bottom = np.zeros(len(diseases))
        
        for j, metric in enumerate(metrics):
            values = performance_data[model][metric]
            bars = ax.bar(diseases, values, bottom=bottom, label=metric, 
                         color=colors_stack[j], alpha=0.8)
            
            # 添加数值标签
            for k, (disease, value) in enumerate(zip(diseases, values)):
                if value > 0.05:  # 只显示较大的值
                    ax.text(k, bottom[k] + value/2, f'{value:.2f}', 
                           ha='center', va='center', fontweight='bold', fontsize=9)
            
            bottom += values
        
        ax.set_title(f'{model}性能指标堆叠图', fontsize=12, fontweight='bold')
        ax.set_ylabel('累积性能分数', fontsize=11)
        ax.legend(fontsize=9, loc='upper left')
        ax.set_ylim(0, 5)
    
    plt.suptitle('三种模型性能指标堆叠对比', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('problem2/images/三种模型性能_堆叠条形图.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 7. 小提琴图 - 展示性能分布
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 准备所有AUC数据
    all_auc_data = []
    labels = []
    
    for model in models:
        auc_values = performance_data[model]['AUC']
        all_auc_data.append(auc_values)
        labels.append(model)
    
    # 绘制小提琴图
    parts = ax.violinplot(all_auc_data, positions=range(1, len(models)+1), showmeans=True, showmedians=True)
    
    # 美化小提琴图
    for pc in parts['bodies']:
        pc.set_facecolor('lightblue')
        pc.set_alpha(0.7)
    
    ax.set_xticks(range(1, len(models)+1))
    ax.set_xticklabels(labels, fontsize=12)
    ax.set_ylabel('AUC分数', fontsize=12, fontweight='bold')
    ax.set_title('三种模型AUC分数分布（小提琴图）', fontsize=14, fontweight='bold')
    ax.grid(True, alpha=0.3)
    ax.set_ylim(0, 1)
    
    plt.tight_layout()
    plt.savefig('problem2/images/三种模型AUC分布_小提琴图.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✓ 多种图表类型已生成")

if __name__ == "__main__":
    print("开始生成多种图表类型...")
    create_various_chart_types()
    print("✅ 多种图表类型生成完成！")
    print("\n生成的图表文件:")
    print("📊 雷达图: *_雷达图.png")
    print("🥧 饼状图: *_饼图.png") 
    print("🔥 热力图: *_热力图.png")
    print("📈 折线图: *_折线图.png")
    print("🔵 散点图: *_散点图.png")
    print("📊 堆叠图: *_堆叠条形图.png")
    print("🎻 小提琴图: *_小提琴图.png")
