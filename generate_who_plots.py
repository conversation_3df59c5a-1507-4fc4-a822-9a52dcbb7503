"""
生成WHO建议报告相关图表
"""
import matplotlib
matplotlib.use('Agg')

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os

# 设置中文字体
try:
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
    plt.rcParams['axes.unicode_minus'] = False
except:
    print("中文字体设置失败，使用默认字体")

def create_who_recommendation_plots():
    """创建WHO建议相关图表"""
    print("生成WHO建议报告图表...")
    
    # 确保目录存在
    os.makedirs('problem4/images', exist_ok=True)
    
    # 1. 风险因子重要性和成本效益分析
    plt.figure(figsize=(16, 12))
    
    # 风险因子重要性评分
    plt.subplot(2, 3, 1)
    risk_factors = ['High Age\n(>65)', 'Hypertension', 'Diabetes', 'Smoking', 'Obesity']
    importance_scores = [0.90, 0.85, 0.78, 0.72, 0.65]
    prevalence = [0.35, 0.30, 0.20, 0.25, 0.40]
    
    bars = plt.bar(risk_factors, importance_scores, color='steelblue', alpha=0.8)
    plt.title('Risk Factor Importance Scores')
    plt.ylabel('Importance Score')
    plt.xticks(rotation=45)
    
    # 添加数值标签
    for bar, score in zip(bars, importance_scores):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.02,
                f'{score:.2f}', ha='center', va='bottom', fontweight='bold')
    
    # 风险因子患病率
    plt.subplot(2, 3, 2)
    bars = plt.bar(risk_factors, prevalence, color='coral', alpha=0.8)
    plt.title('Risk Factor Prevalence in Population')
    plt.ylabel('Prevalence Rate')
    plt.xticks(rotation=45)
    
    # 添加数值标签
    for bar, rate in zip(bars, prevalence):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{rate:.0%}', ha='center', va='bottom', fontweight='bold')
    
    # 干预措施成本效益分析
    plt.subplot(2, 3, 3)
    interventions = ['Physical\nActivity', 'Smoking\nCessation', 'BP Control', 'Healthy\nDiet', 'Diabetes\nManagement', 'Early\nScreening']
    cost_per_qaly = [300, 450, 580, 800, 1200, 2500]
    
    # 根据成本效益设置颜色
    colors = ['green' if c < 1000 else 'orange' if c < 2000 else 'red' for c in cost_per_qaly]
    bars = plt.bar(interventions, cost_per_qaly, color=colors, alpha=0.7)
    plt.title('Cost-Effectiveness Analysis')
    plt.ylabel('Cost per QALY ($)')
    plt.xticks(rotation=45)
    plt.axhline(y=50000, color='red', linestyle='--', alpha=0.7, label='Cost-effective threshold')
    
    # 添加数值标签
    for bar, cost in zip(bars, cost_per_qaly):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 50,
                f'${cost}', ha='center', va='bottom', fontsize=9)
    
    plt.legend()
    
    # 优先级评估矩阵
    plt.subplot(2, 3, 4)
    priorities = [1, 1, 1, 0.5, 0.5, 1]  # 1=高优先级, 0.5=中等优先级
    costs = [0.6, 0.9, 0.8, 0.5, 0.7, 0.8]  # 实施成本
    
    # 根据优先级设置颜色和大小
    colors = ['red' if p == 1 else 'orange' for p in priorities]
    sizes = [300 if p == 1 else 200 for p in priorities]
    
    scatter = plt.scatter(costs, priorities, s=sizes, alpha=0.7, c=colors, edgecolors='black', linewidth=2)
    
    # 添加标签
    for i, intervention in enumerate(interventions):
        plt.annotate(intervention, (costs[i], priorities[i]), 
                    xytext=(10, 10), textcoords='offset points',
                    fontsize=9, fontweight='bold',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
    
    plt.xlabel('Implementation Cost (Relative)')
    plt.ylabel('Priority Level')
    plt.title('WHO Recommendation Priority Matrix')
    plt.ylim(0.3, 1.2)
    
    # 添加象限分割线
    plt.axhline(y=0.75, color='gray', linestyle='--', alpha=0.5)
    plt.axvline(x=0.7, color='gray', linestyle='--', alpha=0.5)
    
    # 预期健康效益
    plt.subplot(2, 3, 5)
    min_impacts = [15, 30, 30, 15, 20, 40]
    max_impacts = [25, 50, 40, 20, 30, 60]
    
    y_pos = np.arange(len(interventions))
    
    # 创建水平条形图
    bars = plt.barh(y_pos, max_impacts, alpha=0.7, color='lightblue', label='Maximum Expected Effect')
    
    # 添加误差条显示范围
    errors = [np.array(max_impacts) - np.array(min_impacts), np.zeros(len(max_impacts))]
    plt.errorbar(max_impacts, y_pos, xerr=errors, fmt='none', 
                capsize=8, color='black', linewidth=2, label='Effect Range')
    
    # 添加最小值标记
    plt.scatter(min_impacts, y_pos, color='red', s=100, zorder=5, label='Minimum Expected Effect')
    
    plt.yticks(y_pos, interventions)
    plt.xlabel('Expected Disease Risk Reduction (%)')
    plt.title('WHO Intervention Impact Assessment')
    
    # 添加数值标签
    for i, (min_val, max_val) in enumerate(zip(min_impacts, max_impacts)):
        plt.text(max_val + 1, i, f'{min_val}-{max_val}%', 
                va='center', fontsize=10, fontweight='bold')
    
    plt.legend(loc='lower right')
    plt.grid(True, alpha=0.3, axis='x')
    
    # 投资回报分析
    plt.subplot(2, 3, 6)
    investment_amounts = [350, 300, 200, 100, 30, 20]  # 百万美元
    roi_ratios = [12.3, 11.8, 9.1, 8.5, 5.8, 4.2]  # 投资回报比
    
    # 气泡图：投资额、回报比
    scatter = plt.scatter(investment_amounts, roi_ratios, 
                         s=[i*2 for i in investment_amounts], 
                         alpha=0.6, c=roi_ratios, cmap='viridis')
    
    plt.xlabel('Investment Amount (Million $)')
    plt.ylabel('Return on Investment Ratio')
    plt.title('Investment vs Return Analysis')
    plt.colorbar(scatter, label='ROI Ratio')
    
    # 添加标签
    for i, intervention in enumerate(interventions):
        plt.annotate(intervention, (investment_amounts[i], roi_ratios[i]), 
                    xytext=(5, 5), textcoords='offset points', fontsize=8)
    
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('problem4/images/who_recommendations_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. 全球疾病负担和干预效果
    plt.figure(figsize=(16, 10))
    
    # 全球疾病负担分布
    plt.subplot(2, 3, 1)
    regions = ['North\nAmerica', 'Europe', 'Asia\nPacific', 'Latin\nAmerica', 'Africa', 'Middle\nEast']
    disease_burden = [1875, 1650, 3200, 980, 1200, 750]  # DALYs (thousands)
    
    bars = plt.bar(regions, disease_burden, color='lightcoral', alpha=0.8)
    plt.title('Global Disease Burden by Region\n(DALYs in thousands)')
    plt.ylabel('Disease Burden (DALYs)')
    plt.xticks(rotation=45)
    
    # 添加数值标签
    for bar, burden in zip(bars, disease_burden):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 50,
                f'{burden}K', ha='center', va='bottom')
    
    # 干预前后对比
    plt.subplot(2, 3, 2)
    diseases = ['Heart\nDisease', 'Stroke', 'Diabetes', 'Combined']
    baseline_cases = [150000, 80000, 120000, 350000]
    prevented_cases = [45000, 20000, 30000, 95000]
    
    x = np.arange(len(diseases))
    width = 0.35
    
    plt.bar(x - width/2, baseline_cases, width, label='Baseline Cases', alpha=0.8, color='red')
    plt.bar(x + width/2, prevented_cases, width, label='Prevented Cases', alpha=0.8, color='green')
    plt.title('Disease Prevention Impact\n(Annual Cases per Million Population)')
    plt.xlabel('Disease Type')
    plt.ylabel('Number of Cases')
    plt.xticks(x, diseases)
    plt.legend()
    
    # 经济效益分析
    plt.subplot(2, 3, 3)
    cost_categories = ['Medical\nCost Savings', 'Productivity\nGains', 'Intervention\nCosts', 'Net\nBenefit']
    amounts = [57.45, 43.88, -10.0, 91.33]  # 十亿美元
    colors = ['green', 'green', 'red', 'blue']
    
    bars = plt.bar(cost_categories, amounts, color=colors, alpha=0.7)
    plt.title('Economic Impact Analysis\n(Billion USD)')
    plt.ylabel('Amount (Billion $)')
    plt.xticks(rotation=45)
    plt.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    
    # 添加数值标签
    for bar, amount in zip(bars, amounts):
        plt.text(bar.get_x() + bar.get_width()/2, 
                bar.get_height() + (2 if amount > 0 else -5),
                f'${amount:.1f}B', ha='center', va='bottom' if amount > 0 else 'top')
    
    # 时间线实施计划
    plt.subplot(2, 3, 4)
    phases = ['Phase 1\n(Year 1)', 'Phase 2\n(Year 2-3)', 'Phase 3\n(Year 4-5)', 'Phase 4\n(Year 6-10)']
    coverage_rates = [25, 50, 75, 90]  # 覆盖率百分比
    
    plt.plot(phases, coverage_rates, 'o-', linewidth=3, markersize=10, color='blue')
    plt.title('Implementation Timeline\n(Population Coverage)')
    plt.ylabel('Coverage Rate (%)')
    plt.grid(True, alpha=0.3)
    
    # 添加数值标签
    for phase, rate in zip(phases, coverage_rates):
        plt.text(phase, rate + 2, f'{rate}%', ha='center', va='bottom', fontweight='bold')
    
    # 不确定性分析
    plt.subplot(2, 3, 5)
    scenarios = ['Pessimistic', 'Realistic', 'Optimistic']
    success_probabilities = [0.65, 0.85, 0.95]
    net_benefits = [45, 91, 150]  # 十亿美元
    
    # 误差条显示不确定性范围
    errors = [[20, 15, 25], [25, 20, 30]]  # 下误差，上误差
    
    bars = plt.bar(scenarios, net_benefits, color=['orange', 'blue', 'green'], alpha=0.7)
    plt.errorbar(scenarios, net_benefits, yerr=errors, fmt='none', 
                capsize=10, color='black', linewidth=2)
    
    plt.title('Uncertainty Analysis\n(Net Economic Benefit)')
    plt.ylabel('Net Benefit (Billion $)')
    
    # 添加成功概率标签
    for i, (bar, prob) in enumerate(zip(bars, success_probabilities)):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 10,
                f'Success: {prob:.0%}', ha='center', va='bottom', fontweight='bold')
    
    # 国际合作网络
    plt.subplot(2, 3, 6)
    cooperation_levels = ['Bilateral', 'Regional', 'Multilateral', 'Global']
    participation_rates = [0.45, 0.65, 0.80, 0.92]
    funding_amounts = [2.5, 4.8, 7.2, 10.0]  # 十亿美元
    
    # 双y轴图
    ax1 = plt.gca()
    bars = ax1.bar(cooperation_levels, participation_rates, alpha=0.7, color='lightblue')
    ax1.set_ylabel('Participation Rate', color='blue')
    ax1.tick_params(axis='y', labelcolor='blue')
    
    ax2 = ax1.twinx()
    line = ax2.plot(cooperation_levels, funding_amounts, 'ro-', linewidth=2, markersize=8)
    ax2.set_ylabel('Funding Amount (Billion $)', color='red')
    ax2.tick_params(axis='y', labelcolor='red')
    
    plt.title('International Cooperation Framework')
    plt.xticks(rotation=45)
    
    plt.tight_layout()
    plt.savefig('problem4/images/global_health_impact.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✓ WHO建议报告图表已生成")

if __name__ == "__main__":
    print("开始生成WHO建议报告图表...")
    create_who_recommendation_plots()
    print("✅ WHO建议图表生成完成！")
    print("\n生成的图表文件:")
    print("📊 problem4/images/who_recommendations_analysis.png")
    print("📊 problem4/images/global_health_impact.png")
