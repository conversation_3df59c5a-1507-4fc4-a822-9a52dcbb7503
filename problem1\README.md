# 问题一：数据预处理与描述统计分析

## 1. 项目概述

本项目实现了三个医疗数据集（心脏病、中风、肝硬化）的完整数据预处理流程，包括缺失值处理、异常值检测、特征编码、类别平衡处理和描述统计分析。

## 2. 数据集信息

### 2.1 心脏病数据集 (heart.csv)
- **样本数量**: 920
- **特征数量**: 12
- **目标变量**: HeartDisease (0/1)
- **主要特征**: 年龄、性别、胸痛类型、静息血压、胆固醇、最大心率等

### 2.2 中风数据集 (stroke.csv)
- **样本数量**: 5,110
- **特征数量**: 12
- **目标变量**: stroke (0/1)
- **主要特征**: 年龄、性别、高血压、心脏病史、平均血糖水平、BMI、吸烟状况等

### 2.3 肝硬化数据集 (cirrhosis.csv)
- **样本数量**: 420
- **特征数量**: 20
- **目标变量**: Status (D/C/CL)
- **主要特征**: 药物类型、年龄、性别、胆红素、胆固醇、白蛋白等生化指标

## 3. 数据预处理方法

### 3.1 缺失值处理
- **MICE (Multiple Imputation by Chained Equations)**
  - 使用随机森林作为估计器
  - 适用于混合数据类型
  - 保持数据分布特性

- **MissForest (针对肝硬化数据)**
  - 基于随机森林的非参数插补
  - 处理复杂的非线性关系

### 3.2 异常值检测
- **IQR方法**
  - 基于四分位距检测极端值
  - 阈值：Q1 - 1.5×IQR 和 Q3 + 1.5×IQR

- **LOF (Local Outlier Factor)**
  - 检测局部异常模式
  - 污染率设置为10%

### 3.3 特征编码与缩放
- **分类变量**: One-Hot编码
- **数值变量**: Z-score标准化
- **偏斜变量**: 对数变换 (log1p)

### 3.4 类别平衡处理
- **SMOTE-Tomek**
  - SMOTE: 合成少数类样本
  - Tomek Links: 清除边界噪声
  - 提高模型对少数类的识别能力

## 4. 统计分析结果

### 4.1 描述性统计
- 计算各特征的均值、标准差、分位数
- 分析目标变量的分布和患病率
- 识别数据的基本特征和分布模式

### 4.2 统计检验
- **数值变量**: t检验比较不同组间差异
- **分类变量**: 卡方检验分析关联性
- **显著性水平**: p < 0.05

### 4.3 相关性分析
- 计算特征间的Pearson相关系数
- 识别高度相关的特征对
- 为特征选择提供依据

## 5. 可视化输出

### 5.1 箱线图 (*_boxplots.png)
- 展示数值特征在不同目标类别下的分布
- 识别组间差异和异常值
- 支持多特征并排比较

### 5.2 相关性热图 (*_correlation_heatmap.png)
- 可视化特征间的线性关系
- 使用颜色编码表示相关强度
- 帮助识别冗余特征

### 5.3 目标变量分布图 (*_target_distribution.png)
- 柱状图和饼图展示类别分布
- 量化类别不平衡程度
- 评估重采样效果

### 5.4 数据概况报告 (*_profile_report.html)
- 完整的探索性数据分析
- 包含缺失值、异常值、分布等详细信息
- 交互式图表和统计摘要

## 6. 主要发现

### 6.1 数据质量
- **心脏病数据**: 无缺失值，数据质量良好
- **中风数据**: BMI字段存在缺失值，需要插补处理
- **肝硬化数据**: 多个生化指标存在缺失值

### 6.2 特征重要性
- 年龄、血压、胆固醇等生理指标显著相关
- 吸烟、糖尿病等生活方式因素影响显著
- 生化指标在肝硬化诊断中作用突出

### 6.3 类别分布
- 心脏病数据相对平衡
- 中风数据存在明显的类别不平衡
- 肝硬化数据需要转换为二分类问题

## 7. 技术实现

### 7.1 核心类: DataPreprocessor
```python
class DataPreprocessor:
    - load_data(): 加载三个数据集
    - mice_imputation(): MICE缺失值插补
    - detect_outliers_iqr(): IQR异常值检测
    - detect_outliers_lof(): LOF异常值检测
    - encode_and_scale(): 特征编码和缩放
    - handle_imbalance(): SMOTE-Tomek平衡处理
    - descriptive_statistics(): 描述性统计
    - statistical_tests(): 统计检验
    - create_visualizations(): 生成可视化图表
```

### 7.2 依赖包管理
- 自动检测和安装缺失的Python包
- 使用清华镜像源加速下载
- 支持conda环境中的包管理

## 8. 文件结构
```
problem1/
├── data_preprocessing.py    # 核心预处理类
├── main.py                 # 主执行脚本
├── test_run.py            # 简化测试脚本
├── requirements.txt       # 依赖包列表
├── README.md             # 本文档
└── 输出文件/
    ├── *_boxplots.png
    ├── *_correlation_heatmap.png
    ├── *_target_distribution.png
    ├── *_profile_report.html
    └── summary_report.md
```

## 9. 使用方法

### 9.1 环境准备
```bash
conda activate play
cd problem1
```

### 9.2 运行分析
```bash
python main.py          # 完整分析流程
python test_run.py       # 简化测试版本
```

### 9.3 查看结果
- 查看生成的PNG图片文件
- 打开HTML概况报告
- 阅读Markdown分析报告

## 10. 下一步工作

1. **特征选择**: 基于统计检验和相关性分析结果
2. **模型构建**: 使用预处理后的数据训练预测模型
3. **性能评估**: 评估不同预处理方法对模型性能的影响
4. **参数优化**: 调整插补和平衡处理的参数

## 11. 参考文献

1. Van Buuren, S. (2018). Multiple imputation by chained equations in R. *Journal of Statistical Software*
2. Stekhoven, D. J., & Bühlmann, P. (2012). MissForest—non-parametric missing value imputation for mixed-type data. *Bioinformatics*
3. Chawla, N. V., et al. (2002). SMOTE: synthetic minority oversampling technique. *Journal of Artificial Intelligence Research*
4. Breunig, M. M., et al. (2000). LOF: identifying density-based local outliers. *ACM SIGMOD Record*
