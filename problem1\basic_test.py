import pandas as pd
import numpy as np

print("开始基础测试...")

try:
    # 测试数据加载
    heart_df = pd.read_csv(r'E:\aaa_garbage\paly\jianmo_test1\data\附件\heart.csv')
    stroke_df = pd.read_csv(r'E:\aaa_garbage\paly\jianmo_test1\data\附件\stroke.csv')
    cirrhosis_df = pd.read_csv(r'E:\aaa_garbage\paly\jianmo_test1\data\附件\cirrhosis.csv')
    
    print(f"✓ 数据加载成功")
    print(f"心脏病数据: {heart_df.shape}")
    print(f"中风数据: {stroke_df.shape}")
    print(f"肝硬化数据: {cirrhosis_df.shape}")
    
    # 基本统计
    print(f"\n心脏病数据缺失值: {heart_df.isnull().sum().sum()}")
    print(f"中风数据缺失值: {stroke_df.isnull().sum().sum()}")
    print(f"肝硬化数据缺失值: {cirrhosis_df.isnull().sum().sum()}")
    
    # 生成简单报告
    with open('basic_analysis_result.md', 'w', encoding='utf-8') as f:
        f.write(f"""# 基础数据分析结果

## 数据集信息

### 心脏病数据集
- 样本数: {heart_df.shape[0]}
- 特征数: {heart_df.shape[1]}
- 缺失值: {heart_df.isnull().sum().sum()}

### 中风数据集
- 样本数: {stroke_df.shape[0]}
- 特征数: {stroke_df.shape[1]}
- 缺失值: {stroke_df.isnull().sum().sum()}

### 肝硬化数据集
- 样本数: {cirrhosis_df.shape[0]}
- 特征数: {cirrhosis_df.shape[1]}
- 缺失值: {cirrhosis_df.isnull().sum().sum()}

## 分析完成

基础数据加载和分析已完成。
""")
    
    print("✓ 基础分析完成，结果已保存到 basic_analysis_result.md")
    
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
