"""
问题一：数据预处理与描述统计
实现数据预处理流程：缺失值处理(MICE/MissForest)、异常值检测(IQR+LOF)、编码缩放、失衡处理(SMOTE-Tomek)、描述统计分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# 数据预处理相关库
from sklearn.experimental import enable_iterative_imputer
from sklearn.impute import IterativeImputer
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.neighbors import LocalOutlierFactor
from sklearn.preprocessing import StandardScaler, LabelEncoder, OneHotEncoder
from sklearn.compose import ColumnTransformer
from imblearn.combine import SMOTETomek
from imblearn.over_sampling import SMOTE
from imblearn.under_sampling import TomekLinks

# 统计分析相关库
from scipy import stats
from scipy.stats import chi2_contingency, ttest_ind
try:
    from ydata_profiling import ProfileReport
except ImportError:
    from pandas_profiling import ProfileReport

# 可视化
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

class DataPreprocessor:
    def __init__(self):
        self.scalers = {}
        self.encoders = {}
        self.imputers = {}
        
    def load_data(self):
        """加载三个数据集"""
        print("正在加载数据集...")
        
        # 加载心脏病数据
        self.heart_df = pd.read_csv('../data/附件/heart.csv')
        print(f"心脏病数据集形状: {self.heart_df.shape}")
        
        # 加载中风数据
        self.stroke_df = pd.read_csv('../data/附件/stroke.csv')
        print(f"中风数据集形状: {self.stroke_df.shape}")
        
        # 加载肝硬化数据
        self.cirrhosis_df = pd.read_csv('../data/附件/cirrhosis.csv')
        print(f"肝硬化数据集形状: {self.cirrhosis_df.shape}")
        
        return self.heart_df, self.stroke_df, self.cirrhosis_df
    
    def explore_missing_values(self, df, dataset_name):
        """探索缺失值情况"""
        print(f"\n=== {dataset_name} 缺失值分析 ===")
        missing_info = df.isnull().sum()
        missing_percent = (missing_info / len(df)) * 100
        
        missing_df = pd.DataFrame({
            '缺失数量': missing_info,
            '缺失百分比': missing_percent
        })
        missing_df = missing_df[missing_df['缺失数量'] > 0].sort_values('缺失百分比', ascending=False)
        
        if len(missing_df) > 0:
            print(missing_df)
        else:
            print("无缺失值")
            
        return missing_df
    
    def mice_imputation(self, df, categorical_cols, numerical_cols):
        """使用MICE方法进行缺失值插补"""
        print("使用MICE方法进行缺失值插补...")
        
        df_imputed = df.copy()
        
        # 对分类变量进行标签编码
        label_encoders = {}
        for col in categorical_cols:
            if col in df_imputed.columns and df_imputed[col].dtype == 'object':
                le = LabelEncoder()
                # 处理缺失值
                df_imputed[col] = df_imputed[col].astype(str)
                df_imputed[col] = le.fit_transform(df_imputed[col])
                label_encoders[col] = le
        
        # 使用MICE插补
        imputer = IterativeImputer(
            estimator=RandomForestRegressor(n_estimators=100, random_state=42),
            random_state=42,
            max_iter=10
        )
        
        df_imputed_values = imputer.fit_transform(df_imputed)
        df_imputed = pd.DataFrame(df_imputed_values, columns=df_imputed.columns, index=df_imputed.index)
        
        # 将分类变量转换回原始标签
        for col, le in label_encoders.items():
            df_imputed[col] = df_imputed[col].round().astype(int)
            # 确保值在有效范围内
            df_imputed[col] = np.clip(df_imputed[col], 0, len(le.classes_) - 1)
            df_imputed[col] = le.inverse_transform(df_imputed[col])
            
        return df_imputed
    
    def detect_outliers_iqr(self, df, numerical_cols):
        """使用IQR方法检测异常值"""
        print("使用IQR方法检测异常值...")
        outliers_info = {}
        
        for col in numerical_cols:
            if col in df.columns:
                Q1 = df[col].quantile(0.25)
                Q3 = df[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                outliers = df[(df[col] < lower_bound) | (df[col] > upper_bound)]
                outliers_info[col] = {
                    'count': len(outliers),
                    'percentage': len(outliers) / len(df) * 100,
                    'lower_bound': lower_bound,
                    'upper_bound': upper_bound
                }
                
                print(f"{col}: {len(outliers)} 个异常值 ({len(outliers)/len(df)*100:.2f}%)")
        
        return outliers_info
    
    def detect_outliers_lof(self, df, numerical_cols, contamination=0.1):
        """使用LOF方法检测异常值"""
        print("使用LOF方法检测异常值...")
        
        if len(numerical_cols) == 0:
            return np.array([])
            
        # 选择数值列
        X = df[numerical_cols].fillna(df[numerical_cols].median())
        
        # 标准化
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # LOF检测
        lof = LocalOutlierFactor(n_neighbors=20, contamination=contamination)
        outlier_labels = lof.fit_predict(X_scaled)
        
        outlier_indices = np.where(outlier_labels == -1)[0]
        print(f"LOF检测到 {len(outlier_indices)} 个异常值 ({len(outlier_indices)/len(df)*100:.2f}%)")
        
        return outlier_indices
    
    def encode_and_scale(self, df, categorical_cols, numerical_cols, target_col):
        """编码和缩放特征"""
        print("进行特征编码和缩放...")
        
        df_processed = df.copy()
        
        # 分离特征和目标变量
        if target_col in df_processed.columns:
            y = df_processed[target_col]
            X = df_processed.drop(columns=[target_col])
        else:
            y = None
            X = df_processed
        
        # 处理分类变量 - One-Hot编码
        categorical_features = [col for col in categorical_cols if col in X.columns]
        numerical_features = [col for col in numerical_cols if col in X.columns]
        
        if categorical_features:
            # One-Hot编码
            encoder = OneHotEncoder(drop='first', sparse_output=False, handle_unknown='ignore')
            encoded_cats = encoder.fit_transform(X[categorical_features])
            
            # 获取编码后的列名
            encoded_feature_names = encoder.get_feature_names_out(categorical_features)
            encoded_df = pd.DataFrame(encoded_cats, columns=encoded_feature_names, index=X.index)
            
            # 合并数值特征和编码后的分类特征
            X_processed = pd.concat([X[numerical_features], encoded_df], axis=1)
            self.encoders['onehot'] = encoder
        else:
            X_processed = X[numerical_features]
        
        # 处理偏斜的数值变量
        for col in numerical_features:
            if col in X_processed.columns:
                skewness = X_processed[col].skew()
                if abs(skewness) > 1:  # 如果偏斜度大于1，进行对数变换
                    X_processed[col] = np.log1p(X_processed[col] - X_processed[col].min() + 1)
                    print(f"{col} 进行了对数变换 (原偏斜度: {skewness:.2f})")
        
        # 标准化数值特征
        if numerical_features:
            scaler = StandardScaler()
            X_processed[numerical_features] = scaler.fit_transform(X_processed[numerical_features])
            self.scalers['standard'] = scaler
        
        return X_processed, y
    
    def handle_imbalance(self, X, y):
        """使用SMOTE-Tomek处理类别不平衡"""
        print("使用SMOTE-Tomek处理类别不平衡...")
        
        # 检查类别分布
        print("原始类别分布:")
        print(pd.Series(y).value_counts())
        
        # 应用SMOTE-Tomek
        smote_tomek = SMOTETomek(random_state=42)
        X_resampled, y_resampled = smote_tomek.fit_resample(X, y)
        
        print("重采样后类别分布:")
        print(pd.Series(y_resampled).value_counts())
        
        return X_resampled, y_resampled

    def descriptive_statistics(self, df, dataset_name, target_col):
        """生成描述性统计"""
        print(f"\n=== {dataset_name} 描述性统计 ===")

        # 基本统计信息
        print("\n基本信息:")
        print(f"样本数量: {len(df)}")
        print(f"特征数量: {df.shape[1]}")

        # 目标变量分布
        if target_col in df.columns:
            print(f"\n{target_col} 分布:")
            target_dist = df[target_col].value_counts()
            print(target_dist)
            print(f"患病率: {target_dist[1]/len(df)*100:.2f}%" if 1 in target_dist.index else "")

        # 数值变量统计
        numerical_cols = df.select_dtypes(include=[np.number]).columns
        if len(numerical_cols) > 0:
            print(f"\n数值变量统计 (n={len(numerical_cols)}):")
            stats_df = df[numerical_cols].describe()
            print(stats_df.round(2))

        # 分类变量统计
        categorical_cols = df.select_dtypes(include=['object']).columns
        if len(categorical_cols) > 0:
            print(f"\n分类变量统计 (n={len(categorical_cols)}):")
            for col in categorical_cols:
                print(f"\n{col}:")
                print(df[col].value_counts().head())

        return {
            'numerical_stats': df[numerical_cols].describe() if len(numerical_cols) > 0 else None,
            'categorical_stats': {col: df[col].value_counts() for col in categorical_cols}
        }

    def statistical_tests(self, df, target_col):
        """进行统计检验"""
        print(f"\n=== 统计检验 ===")

        results = {}

        # 数值变量的t检验
        numerical_cols = df.select_dtypes(include=[np.number]).columns
        numerical_cols = [col for col in numerical_cols if col != target_col]

        for col in numerical_cols:
            if col in df.columns and target_col in df.columns:
                group0 = df[df[target_col] == 0][col].dropna()
                group1 = df[df[target_col] == 1][col].dropna()

                if len(group0) > 0 and len(group1) > 0:
                    t_stat, p_value = ttest_ind(group0, group1)
                    results[col] = {
                        'test': 't-test',
                        'statistic': t_stat,
                        'p_value': p_value,
                        'significant': p_value < 0.05
                    }
                    print(f"{col}: t={t_stat:.3f}, p={p_value:.3f} {'*' if p_value < 0.05 else ''}")

        # 分类变量的卡方检验
        categorical_cols = df.select_dtypes(include=['object']).columns

        for col in categorical_cols:
            if col in df.columns and target_col in df.columns:
                contingency_table = pd.crosstab(df[col], df[target_col])
                if contingency_table.shape[0] > 1 and contingency_table.shape[1] > 1:
                    chi2, p_value, dof, expected = chi2_contingency(contingency_table)
                    results[col] = {
                        'test': 'chi-square',
                        'statistic': chi2,
                        'p_value': p_value,
                        'significant': p_value < 0.05
                    }
                    print(f"{col}: χ²={chi2:.3f}, p={p_value:.3f} {'*' if p_value < 0.05 else ''}")

        return results

    def create_visualizations(self, df, dataset_name, target_col):
        """创建可视化图表"""
        print(f"为 {dataset_name} 创建可视化图表...")

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

        # 1. 箱线图 - 数值变量
        numerical_cols = df.select_dtypes(include=[np.number]).columns
        numerical_cols = [col for col in numerical_cols if col != target_col]

        if len(numerical_cols) > 0:
            n_cols = min(3, len(numerical_cols))
            n_rows = (len(numerical_cols) + n_cols - 1) // n_cols

            fig, axes = plt.subplots(n_rows, n_cols, figsize=(15, 5*n_rows))
            if n_rows == 1:
                axes = [axes] if n_cols == 1 else axes
            else:
                axes = axes.flatten()

            for i, col in enumerate(numerical_cols[:len(axes)]):
                if target_col in df.columns:
                    df_plot = df[[col, target_col]].dropna()
                    sns.boxplot(data=df_plot, x=target_col, y=col, ax=axes[i])
                    axes[i].set_title(f'{col} 按 {target_col} 分组')
                else:
                    sns.boxplot(data=df, y=col, ax=axes[i])
                    axes[i].set_title(f'{col} 分布')

            # 隐藏多余的子图
            for i in range(len(numerical_cols), len(axes)):
                axes[i].set_visible(False)

            plt.tight_layout()
            plt.savefig(f'{dataset_name}_boxplots.png', dpi=300, bbox_inches='tight')
            plt.close()

        # 2. 相关性热图
        if len(numerical_cols) > 1:
            plt.figure(figsize=(12, 10))
            correlation_matrix = df[numerical_cols].corr()

            mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
            sns.heatmap(correlation_matrix, mask=mask, annot=True, cmap='coolwarm',
                       center=0, square=True, fmt='.2f')
            plt.title(f'{dataset_name} 特征相关性热图')
            plt.tight_layout()
            plt.savefig(f'{dataset_name}_correlation_heatmap.png', dpi=300, bbox_inches='tight')
            plt.close()

        # 3. 目标变量分布
        if target_col in df.columns:
            plt.figure(figsize=(8, 6))
            target_counts = df[target_col].value_counts()

            plt.subplot(1, 2, 1)
            target_counts.plot(kind='bar')
            plt.title(f'{dataset_name} {target_col} 分布')
            plt.xlabel(target_col)
            plt.ylabel('数量')

            plt.subplot(1, 2, 2)
            plt.pie(target_counts.values, labels=target_counts.index, autopct='%1.1f%%')
            plt.title(f'{dataset_name} {target_col} 比例')

            plt.tight_layout()
            plt.savefig(f'{dataset_name}_target_distribution.png', dpi=300, bbox_inches='tight')
            plt.close()

    def generate_profile_report(self, df, dataset_name):
        """生成数据概况报告"""
        print(f"为 {dataset_name} 生成概况报告...")

        try:
            profile = ProfileReport(df, title=f'{dataset_name} 数据概况报告',
                                  explorative=True, minimal=False)
            profile.to_file(f'{dataset_name}_profile_report.html')
            print(f"概况报告已保存为 {dataset_name}_profile_report.html")
        except Exception as e:
            print(f"生成概况报告时出错: {e}")

    def process_dataset(self, df, dataset_name, categorical_cols, numerical_cols, target_col):
        """处理单个数据集的完整流程"""
        print(f"\n{'='*50}")
        print(f"处理数据集: {dataset_name}")
        print(f"{'='*50}")

        # 1. 探索缺失值
        missing_info = self.explore_missing_values(df, dataset_name)

        # 2. 缺失值处理
        if len(missing_info) > 0:
            df_imputed = self.mice_imputation(df, categorical_cols, numerical_cols)
        else:
            df_imputed = df.copy()

        # 3. 异常值检测
        outliers_iqr = self.detect_outliers_iqr(df_imputed, numerical_cols)
        outliers_lof = self.detect_outliers_lof(df_imputed, numerical_cols)

        # 4. 描述性统计
        desc_stats = self.descriptive_statistics(df_imputed, dataset_name, target_col)

        # 5. 统计检验
        if target_col in df_imputed.columns:
            stat_tests = self.statistical_tests(df_imputed, target_col)
        else:
            stat_tests = {}

        # 6. 特征编码和缩放
        X_processed, y = self.encode_and_scale(df_imputed, categorical_cols, numerical_cols, target_col)

        # 7. 处理类别不平衡
        if y is not None and len(np.unique(y)) == 2:
            X_balanced, y_balanced = self.handle_imbalance(X_processed, y)
        else:
            X_balanced, y_balanced = X_processed, y

        # 8. 创建可视化
        self.create_visualizations(df_imputed, dataset_name, target_col)

        # 9. 生成概况报告
        self.generate_profile_report(df_imputed, dataset_name)

        return {
            'original_data': df,
            'imputed_data': df_imputed,
            'processed_features': X_processed,
            'balanced_features': X_balanced,
            'target': y,
            'balanced_target': y_balanced,
            'missing_info': missing_info,
            'outliers_iqr': outliers_iqr,
            'outliers_lof': outliers_lof,
            'descriptive_stats': desc_stats,
            'statistical_tests': stat_tests
        }
