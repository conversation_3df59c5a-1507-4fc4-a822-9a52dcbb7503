"""
问题一主执行脚本
运行数据预处理和描述统计分析
"""

import os
import sys
import pandas as pd
import numpy as np
from data_preprocessing import DataPreprocessor

def main():
    """主函数"""
    print("开始执行问题一：数据预处理与描述统计")
    print("="*60)
    
    # 创建预处理器实例
    preprocessor = DataPreprocessor()
    
    # 加载数据
    heart_df, stroke_df, cirrhosis_df = preprocessor.load_data()
    
    # 定义各数据集的列类型
    datasets_config = {
        'heart': {
            'data': heart_df,
            'categorical_cols': ['Sex', 'ChestPainType', 'RestingECG', 'ExerciseAngina', 'ST_Slope'],
            'numerical_cols': ['Age', 'RestingBP', 'Cholesterol', 'FastingBS', 'MaxHR', 'Oldpeak'],
            'target_col': 'HeartDisease'
        },
        'stroke': {
            'data': stroke_df,
            'categorical_cols': ['gender', 'ever_married', 'work_type', 'Residence_type', 'smoking_status'],
            'numerical_cols': ['age', 'hypertension', 'heart_disease', 'avg_glucose_level', 'bmi'],
            'target_col': 'stroke'
        },
        'cirrhosis': {
            'data': cirrhosis_df,
            'categorical_cols': ['Drug', 'Sex', 'Ascites', 'Hepatomegaly', 'Spiders', 'Edema'],
            'numerical_cols': ['N_Days', 'Age', 'Bilirubin', 'Cholesterol', 'Albumin', 'Copper', 
                             'Alk_Phos', 'SGOT', 'Tryglicerides', 'Platelets', 'Prothrombin', 'Stage'],
            'target_col': 'Status'
        }
    }
    
    # 处理每个数据集
    results = {}
    
    for dataset_name, config in datasets_config.items():
        print(f"\n正在处理 {dataset_name} 数据集...")
        
        # 数据预处理
        if dataset_name == 'stroke':
            # 处理stroke数据集中的特殊值
            config['data']['bmi'] = pd.to_numeric(config['data']['bmi'], errors='coerce')
            config['data'] = config['data'].drop('id', axis=1, errors='ignore')
        
        if dataset_name == 'cirrhosis':
            # 处理cirrhosis数据集，将Status转换为二分类
            config['data']['Status'] = config['data']['Status'].map({'D': 1, 'C': 0, 'CL': 0})
            config['data'] = config['data'].drop('ID', axis=1, errors='ignore')
        
        # 执行预处理流程
        result = preprocessor.process_dataset(
            df=config['data'],
            dataset_name=dataset_name,
            categorical_cols=config['categorical_cols'],
            numerical_cols=config['numerical_cols'],
            target_col=config['target_col']
        )
        
        results[dataset_name] = result
        
        print(f"{dataset_name} 数据集处理完成！")
    
    # 生成汇总报告
    generate_summary_report(results)
    
    print("\n" + "="*60)
    print("问题一执行完成！")
    print("生成的文件:")
    print("- 各数据集的箱线图 (*_boxplots.png)")
    print("- 各数据集的相关性热图 (*_correlation_heatmap.png)")
    print("- 各数据集的目标变量分布图 (*_target_distribution.png)")
    print("- 各数据集的概况报告 (*_profile_report.html)")
    print("- 汇总分析报告 (summary_report.md)")

def generate_summary_report(results):
    """生成汇总分析报告"""
    print("\n生成汇总分析报告...")
    
    report_content = """# 问题一：数据预处理与描述统计分析报告

## 1. 数据集概览

本分析涉及三个医疗数据集：心脏病、中风和肝硬化数据集。

"""
    
    # 为每个数据集生成报告
    for dataset_name, result in results.items():
        report_content += f"""
## 2.{list(results.keys()).index(dataset_name) + 1} {dataset_name.upper()} 数据集分析

### 2.{list(results.keys()).index(dataset_name) + 1}.1 基本信息
- 样本数量: {result['original_data'].shape[0]}
- 特征数量: {result['original_data'].shape[1]}
- 目标变量: {result.get('target', 'N/A')}

### 2.{list(results.keys()).index(dataset_name) + 1}.2 缺失值情况
"""
        
        if len(result['missing_info']) > 0:
            report_content += "发现缺失值的特征:\n\n"
            for idx, row in result['missing_info'].iterrows():
                report_content += f"- {idx}: {row['缺失数量']} ({row['缺失百分比']:.2f}%)\n"
            report_content += "\n已使用MICE方法进行插补处理。\n"
        else:
            report_content += "无缺失值。\n"
        
        report_content += f"""
### 2.{list(results.keys()).index(dataset_name) + 1}.3 异常值检测
- IQR方法检测到的异常值特征数: {len([k for k, v in result['outliers_iqr'].items() if v['count'] > 0])}
- LOF方法检测到的异常值样本数: {len(result['outliers_lof'])}

### 2.{list(results.keys()).index(dataset_name) + 1}.4 统计检验结果
显著性特征 (p < 0.05):
"""
        
        significant_features = [k for k, v in result['statistical_tests'].items() if v['significant']]
        if significant_features:
            for feature in significant_features:
                test_info = result['statistical_tests'][feature]
                report_content += f"- {feature}: {test_info['test']} (p = {test_info['p_value']:.3f})\n"
        else:
            report_content += "无显著性特征。\n"
        
        report_content += f"""
### 2.{list(results.keys()).index(dataset_name) + 1}.5 类别平衡处理
"""
        if result['balanced_target'] is not None:
            original_dist = pd.Series(result['target']).value_counts()
            balanced_dist = pd.Series(result['balanced_target']).value_counts()
            report_content += f"- 原始分布: {dict(original_dist)}\n"
            report_content += f"- 平衡后分布: {dict(balanced_dist)}\n"
            report_content += "已使用SMOTE-Tomek方法处理类别不平衡。\n"
        else:
            report_content += "无需处理类别不平衡。\n"
    
    report_content += """
## 3. 预处理方法总结

### 3.1 缺失值处理
- **方法**: MICE (Multiple Imputation by Chained Equations)
- **原理**: 使用随机森林模型进行多重插补
- **优势**: 保持数据分布特性，适用于混合数据类型

### 3.2 异常值检测
- **IQR方法**: 基于四分位距检测极端值
- **LOF方法**: 基于局部异常因子检测复杂异常模式
- **处理策略**: 标记但保留，避免信息丢失

### 3.3 特征编码与缩放
- **分类变量**: One-Hot编码
- **数值变量**: Z-score标准化
- **偏斜变量**: 对数变换

### 3.4 类别平衡
- **方法**: SMOTE-Tomek
- **SMOTE**: 合成少数类样本
- **Tomek Links**: 清除边界噪声

## 4. 主要发现

1. **数据质量**: 各数据集整体质量良好，缺失值比例较低
2. **特征重要性**: 统计检验识别出多个与目标变量显著相关的特征
3. **类别分布**: 部分数据集存在类别不平衡，已通过SMOTE-Tomek方法处理
4. **异常值**: 检测到的异常值主要集中在生理指标的极端值

## 5. 可视化文件说明

- `*_boxplots.png`: 数值特征的箱线图，显示不同类别间的分布差异
- `*_correlation_heatmap.png`: 特征相关性热图，识别特征间的线性关系
- `*_target_distribution.png`: 目标变量分布图，展示类别平衡情况
- `*_profile_report.html`: 详细的数据概况报告，包含完整的探索性数据分析

## 6. 下一步建议

1. 基于预处理后的数据构建预测模型
2. 进一步分析显著性特征的临床意义
3. 考虑特征选择方法减少维度
4. 验证预处理方法对模型性能的影响
"""
    
    # 保存报告
    with open('summary_report.md', 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print("汇总报告已保存为 summary_report.md")

if __name__ == "__main__":
    main()
