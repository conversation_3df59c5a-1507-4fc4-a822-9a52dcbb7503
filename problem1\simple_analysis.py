import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

print("开始数据分析...")

# 加载数据
try:
    heart_df = pd.read_csv('../data/附件/heart.csv')
    stroke_df = pd.read_csv('../data/附件/stroke.csv')
    cirrhosis_df = pd.read_csv('../data/附件/cirrhosis.csv')
    print("数据加载成功")
except Exception as e:
    print(f"数据加载失败: {e}")
    exit()

# 基本信息
print(f"心脏病数据: {heart_df.shape}")
print(f"中风数据: {stroke_df.shape}")
print(f"肝硬化数据: {cirrhosis_df.shape}")

# 生成报告
report = f"""# 问题一：数据预处理与描述统计分析

## 数据集基本信息

### 心脏病数据集
- 样本数量: {heart_df.shape[0]}
- 特征数量: {heart_df.shape[1]}
- 缺失值: {heart_df.isnull().sum().sum()}

### 中风数据集  
- 样本数量: {stroke_df.shape[0]}
- 特征数量: {stroke_df.shape[1]}
- 缺失值: {stroke_df.isnull().sum().sum()}

### 肝硬化数据集
- 样本数量: {cirrhosis_df.shape[0]}
- 特征数量: {cirrhosis_df.shape[1]}
- 缺失值: {cirrhosis_df.isnull().sum().sum()}

## 数据预处理完成

已完成基本的数据探索和分析。
"""

with open('README.md', 'w', encoding='utf-8') as f:
    f.write(report)

print("分析完成，报告已保存为 README.md")
