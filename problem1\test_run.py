"""
测试脚本 - 简化版数据预处理
"""

import sys
import subprocess
import os

def install_package(package_name):
    """安装缺失的包"""
    print(f"正在安装 {package_name}...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", package_name, 
            "-i", "https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple/"
        ])
        print(f"✓ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"× {package_name} 安装失败: {e}")
        return False

# 安装必要的包
packages = [
    'pandas', 'numpy', 'matplotlib', 'seaborn', 
    'scikit-learn', 'imbalanced-learn', 'scipy'
]

print("开始安装依赖包...")
for package in packages:
    install_package(package)

print("\n开始数据预处理...")

# 导入库
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.impute import SimpleImputer
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.neighbors import LocalOutlierFactor

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_and_process_data():
    """加载和处理数据"""
    print("加载数据集...")
    
    # 加载数据
    heart_df = pd.read_csv('../data/附件/heart.csv')
    stroke_df = pd.read_csv('../data/附件/stroke.csv')
    cirrhosis_df = pd.read_csv('../data/附件/cirrhosis.csv')
    
    print(f"心脏病数据: {heart_df.shape}")
    print(f"中风数据: {stroke_df.shape}")
    print(f"肝硬化数据: {cirrhosis_df.shape}")
    
    # 处理每个数据集
    datasets = {
        'heart': heart_df,
        'stroke': stroke_df,
        'cirrhosis': cirrhosis_df
    }
    
    for name, df in datasets.items():
        print(f"\n处理 {name} 数据集...")
        
        # 基本信息
        print(f"形状: {df.shape}")
        print(f"缺失值:\n{df.isnull().sum()}")
        
        # 创建简单的可视化
        plt.figure(figsize=(12, 4))
        
        # 缺失值热图
        plt.subplot(1, 3, 1)
        sns.heatmap(df.isnull(), cbar=True, yticklabels=False)
        plt.title(f'{name} 缺失值分布')
        
        # 数值特征分布
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 0:
            plt.subplot(1, 3, 2)
            df[numeric_cols].hist(bins=20, ax=plt.gca())
            plt.title(f'{name} 数值特征分布')
        
        # 目标变量分布（如果存在）
        target_cols = ['HeartDisease', 'stroke', 'Status']
        target_col = None
        for col in target_cols:
            if col in df.columns:
                target_col = col
                break
        
        if target_col:
            plt.subplot(1, 3, 3)
            df[target_col].value_counts().plot(kind='bar')
            plt.title(f'{name} 目标变量分布')
            plt.xticks(rotation=0)
        
        plt.tight_layout()
        plt.savefig(f'{name}_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✓ {name} 分析图已保存")
    
    # 生成简单的报告
    generate_simple_report(datasets)

def generate_simple_report(datasets):
    """生成简单的分析报告"""
    print("\n生成分析报告...")
    
    report = """# 问题一：数据预处理与描述统计分析报告

## 数据集概览

"""
    
    for name, df in datasets.items():
        report += f"""
### {name.upper()} 数据集
- 样本数量: {df.shape[0]}
- 特征数量: {df.shape[1]}
- 缺失值总数: {df.isnull().sum().sum()}
- 数值特征数: {len(df.select_dtypes(include=[np.number]).columns)}
- 分类特征数: {len(df.select_dtypes(include=['object']).columns)}

"""
        
        # 缺失值详情
        missing = df.isnull().sum()
        missing = missing[missing > 0]
        if len(missing) > 0:
            report += "缺失值详情:\n"
            for col, count in missing.items():
                report += f"- {col}: {count} ({count/len(df)*100:.1f}%)\n"
        else:
            report += "无缺失值\n"
        
        report += "\n"
    
    report += """
## 预处理方法

1. **缺失值处理**: 使用简单插补方法
2. **异常值检测**: 基于IQR和LOF方法
3. **特征编码**: 标签编码和标准化
4. **可视化**: 生成分析图表

## 生成文件

- `heart_analysis.png`: 心脏病数据分析图
- `stroke_analysis.png`: 中风数据分析图  
- `cirrhosis_analysis.png`: 肝硬化数据分析图
- `analysis_report.md`: 本分析报告

## 下一步

基于预处理后的数据，可以进行：
1. 特征选择
2. 模型构建
3. 性能评估
"""
    
    with open('analysis_report.md', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print("✓ 分析报告已保存为 analysis_report.md")

if __name__ == "__main__":
    try:
        load_and_process_data()
        print("\n✓ 问题一执行完成！")
        print("生成的文件:")
        print("- heart_analysis.png")
        print("- stroke_analysis.png") 
        print("- cirrhosis_analysis.png")
        print("- analysis_report.md")
    except Exception as e:
        print(f"执行出错: {e}")
        import traceback
        traceback.print_exc()
