# 问题二：三病预测模型

## 1. 项目概述

本项目构建了心脏病、中风和肝硬化三种疾病的机器学习预测模型，使用多种算法进行建模和比较，并通过SHAP分析提供模型解释性。

## 2. 模型架构

### 2.1 算法选择
- **逻辑回归 (Logistic Regression)**: 基线模型，具有良好的可解释性
- **随机森林 (Random Forest)**: 处理非线性关系，提供特征重要性
- **梯度提升 (Gradient Boosting)**: 高性能集成学习算法
- **XGBoost**: 优化的梯度提升算法（如果可用）

### 2.2 模型配置
- **心脏病模型**: XGBoost (500棵树，深度4) + 随机森林 (300棵树)
- **中风模型**: 随机森林 (300棵树) + 逻辑回归
- **肝硬化模型**: 梯度提升 + 随机森林（二分类：死亡 vs 存活）

## 3. 数据预处理

### 3.1 心脏病数据
- **样本数**: 920
- **特征数**: 11
- **目标变量**: HeartDisease (0/1)
- **预处理**: 标签编码 + 标准化
- **数据质量**: 无缺失值

### 3.2 中风数据
- **样本数**: 5,110
- **特征数**: 11 (删除ID列)
- **目标变量**: stroke (0/1)
- **预处理**: BMI缺失值填充 + 标签编码 + 标准化
- **特殊处理**: 类别不平衡问题

### 3.3 肝硬化数据
- **样本数**: 420
- **特征数**: 19 (删除ID列)
- **目标变量**: Status (D=1死亡, C/CL=0存活)
- **预处理**: 缺失值填充 + 标签编码 + 标准化
- **转换**: 多分类转二分类

## 4. 模型评估

### 4.1 评估指标
- **AUC-ROC**: 主要评估指标，衡量分类性能
- **F1分数**: 平衡精确率和召回率
- **准确率**: 整体分类正确率
- **精确率**: 正类预测的准确性
- **召回率**: 正类识别的完整性

### 4.2 验证方法
- **数据分割**: 80% 训练，20% 测试
- **交叉验证**: 5折分层交叉验证
- **性能稳定性**: 计算均值和标准差

## 5. 模型解释

### 5.1 特征重要性
- 基于树模型的内置特征重要性
- 排序显示前10个重要特征
- 可视化特征贡献度

### 5.2 SHAP分析
- **TreeExplainer**: 用于树模型
- **LinearExplainer**: 用于逻辑回归
- **Summary Plot**: 特征重要性总结
- **样本解释**: 前100个测试样本

## 6. 可视化输出

### 6.1 模型评估图表 (*_model_evaluation.png)
- **性能比较**: 多个模型的指标对比柱状图
- **ROC曲线**: 所有模型的ROC曲线和AUC值
- **特征重要性**: 最佳模型的特征重要性排序
- **混淆矩阵**: 最佳模型的分类结果热图

### 6.2 SHAP解释图表 (*_shap_summary.png)
- **特征重要性**: SHAP值的特征重要性排序
- **特征影响**: 每个特征对预测结果的正负影响
- **样本分析**: 不同样本的特征贡献模式

## 7. 技术实现

### 7.1 核心类: DiseasePredictor
```python
class DiseasePredictor:
    - load_and_preprocess_data(): 加载和预处理三个数据集
    - preprocess_heart_data(): 心脏病数据预处理
    - preprocess_stroke_data(): 中风数据预处理  
    - preprocess_cirrhosis_data(): 肝硬化数据预处理
    - build_models(): 构建和训练多个模型
    - evaluate_and_visualize(): 模型评估和可视化
    - shap_analysis(): SHAP模型解释分析
    - generate_report(): 生成综合分析报告
```

### 7.2 依赖管理
- 自动检测和安装缺失包
- 使用清华镜像源加速下载
- 可选包的优雅降级处理

## 8. 预期结果

### 8.1 模型性能目标
- **心脏病预测**: AUC > 0.85, F1 > 0.80
- **中风预测**: AUC > 0.80, F1 > 0.75 (考虑类别不平衡)
- **肝硬化预测**: AUC > 0.75, F1 > 0.70

### 8.2 临床应用价值
- **风险评估**: 为临床医生提供客观的风险评分
- **早期筛查**: 识别高风险患者进行重点监测
- **个性化医疗**: 基于个体特征制定治疗方案
- **资源配置**: 优化医疗资源的分配和使用

## 9. 文件结构
```
problem2/
├── disease_prediction.py    # 核心预测类
├── main.py                 # 主执行脚本
├── README.md              # 本文档
└── 输出文件/
    ├── heart_model_evaluation.png
    ├── stroke_model_evaluation.png
    ├── cirrhosis_model_evaluation.png
    ├── heart_shap_summary.png
    ├── stroke_shap_summary.png
    ├── cirrhosis_shap_summary.png
    └── model_analysis_report.md
```

## 10. 使用方法

### 10.1 环境准备
```bash
conda activate play
cd problem2
```

### 10.2 运行分析
```bash
python main.py
```

### 10.3 查看结果
- 查看生成的PNG评估图表
- 阅读模型分析报告
- 分析SHAP解释结果

## 11. 模型优化建议

### 11.1 超参数调优
- 使用网格搜索或贝叶斯优化
- 针对不同数据集调整参数
- 考虑计算资源和时间成本

### 11.2 特征工程
- 创建组合特征和交互项
- 基于领域知识构造新特征
- 使用特征选择方法降维

### 11.3 集成学习
- 组合多个模型的预测结果
- 使用投票或加权平均
- 考虑模型的多样性和互补性

## 12. 局限性与改进

### 12.1 数据局限性
- 样本量相对较小（特别是肝硬化数据）
- 缺乏时间序列信息
- 可能存在选择偏倚

### 12.2 模型局限性
- 未考虑特征间的复杂交互
- 缺乏外部验证数据集
- 模型解释性仍有提升空间

### 12.3 改进方向
- 收集更大规模的数据集
- 引入深度学习方法
- 开发在线学习和模型更新机制
- 进行多中心外部验证

## 13. 参考文献

1. Breiman, L. (2001). Random forests. *Machine learning*
2. Chen, T., & Guestrin, C. (2016). XGBoost: A scalable tree boosting system
3. Lundberg, S. M., & Lee, S. I. (2017). A unified approach to interpreting model predictions
4. Pedregosa, F., et al. (2011). Scikit-learn: Machine learning in Python
