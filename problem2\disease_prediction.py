"""
问题二：三病预测模型
构建心脏病、中风、肝硬化的预测模型，使用Logistic回归和树模型，进行模型评估和SHAP解释
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

# 机器学习相关库
from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    roc_auc_score, roc_curve, confusion_matrix, classification_report
)
from sklearn.impute import SimpleImputer
import joblib

# 尝试导入XGBoost和SHAP
try:
    import xgboost as xgb
    XGB_AVAILABLE = True
except ImportError:
    XGB_AVAILABLE = False
    print("XGBoost未安装，将使用GradientBoosting替代")

try:
    import shap
    SHAP_AVAILABLE = True
except ImportError:
    SHAP_AVAILABLE = False
    print("SHAP未安装，将跳过模型解释部分")

class DiseasePredictor:
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.encoders = {}
        self.results = {}
        
    def load_and_preprocess_data(self):
        """加载和预处理数据"""
        print("加载数据集...")
        
        # 加载数据
        self.heart_df = pd.read_csv('../data/附件/heart.csv')
        self.stroke_df = pd.read_csv('../data/附件/stroke.csv')
        self.cirrhosis_df = pd.read_csv('../data/附件/cirrhosis.csv')
        
        print(f"心脏病数据: {self.heart_df.shape}")
        print(f"中风数据: {self.stroke_df.shape}")
        print(f"肝硬化数据: {self.cirrhosis_df.shape}")
        
        # 预处理各数据集
        self.datasets = {
            'heart': self.preprocess_heart_data(),
            'stroke': self.preprocess_stroke_data(),
            'cirrhosis': self.preprocess_cirrhosis_data()
        }
        
        return self.datasets
    
    def preprocess_heart_data(self):
        """预处理心脏病数据"""
        print("预处理心脏病数据...")
        df = self.heart_df.copy()
        
        # 分离特征和目标
        X = df.drop('HeartDisease', axis=1)
        y = df['HeartDisease']
        
        # 编码分类变量
        categorical_cols = ['Sex', 'ChestPainType', 'RestingECG', 'ExerciseAngina', 'ST_Slope']
        X_encoded = X.copy()
        
        for col in categorical_cols:
            le = LabelEncoder()
            X_encoded[col] = le.fit_transform(X[col])
            self.encoders[f'heart_{col}'] = le
        
        # 标准化数值特征
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X_encoded)
        X_scaled = pd.DataFrame(X_scaled, columns=X_encoded.columns)
        self.scalers['heart'] = scaler
        
        return {
            'X': X_scaled,
            'y': y,
            'feature_names': X_encoded.columns.tolist(),
            'target_name': 'HeartDisease'
        }
    
    def preprocess_stroke_data(self):
        """预处理中风数据"""
        print("预处理中风数据...")
        df = self.stroke_df.copy()
        
        # 删除ID列
        df = df.drop('id', axis=1, errors='ignore')
        
        # 处理BMI缺失值
        df['bmi'] = pd.to_numeric(df['bmi'], errors='coerce')
        df['bmi'].fillna(df['bmi'].median(), inplace=True)
        
        # 分离特征和目标
        X = df.drop('stroke', axis=1)
        y = df['stroke']
        
        # 编码分类变量
        categorical_cols = ['gender', 'ever_married', 'work_type', 'Residence_type', 'smoking_status']
        X_encoded = X.copy()
        
        for col in categorical_cols:
            le = LabelEncoder()
            X_encoded[col] = le.fit_transform(X[col].astype(str))
            self.encoders[f'stroke_{col}'] = le
        
        # 标准化数值特征
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X_encoded)
        X_scaled = pd.DataFrame(X_scaled, columns=X_encoded.columns)
        self.scalers['stroke'] = scaler
        
        return {
            'X': X_scaled,
            'y': y,
            'feature_names': X_encoded.columns.tolist(),
            'target_name': 'stroke'
        }
    
    def preprocess_cirrhosis_data(self):
        """预处理肝硬化数据"""
        print("预处理肝硬化数据...")
        df = self.cirrhosis_df.copy()
        
        # 删除ID列
        df = df.drop('ID', axis=1, errors='ignore')
        
        # 将Status转换为二分类 (D=1, C/CL=0)
        df['Status'] = df['Status'].map({'D': 1, 'C': 0, 'CL': 0})
        
        # 处理缺失值
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        categorical_cols = df.select_dtypes(include=['object']).columns
        
        # 数值列用中位数填充
        for col in numeric_cols:
            if col != 'Status':
                df[col].fillna(df[col].median(), inplace=True)
        
        # 分类列用众数填充
        for col in categorical_cols:
            df[col].fillna(df[col].mode()[0], inplace=True)
        
        # 分离特征和目标
        X = df.drop('Status', axis=1)
        y = df['Status']
        
        # 编码分类变量
        categorical_cols = ['Drug', 'Sex', 'Ascites', 'Hepatomegaly', 'Spiders', 'Edema']
        X_encoded = X.copy()
        
        for col in categorical_cols:
            if col in X_encoded.columns:
                le = LabelEncoder()
                X_encoded[col] = le.fit_transform(X[col].astype(str))
                self.encoders[f'cirrhosis_{col}'] = le
        
        # 标准化数值特征
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X_encoded)
        X_scaled = pd.DataFrame(X_scaled, columns=X_encoded.columns)
        self.scalers['cirrhosis'] = scaler
        
        return {
            'X': X_scaled,
            'y': y,
            'feature_names': X_encoded.columns.tolist(),
            'target_name': 'Status'
        }
    
    def build_models(self, dataset_name, X, y):
        """构建预测模型"""
        print(f"为{dataset_name}构建预测模型...")
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # 定义模型
        models = {
            'Logistic_Regression': LogisticRegression(random_state=42, max_iter=1000),
            'Random_Forest': RandomForestClassifier(n_estimators=300, random_state=42),
            'Gradient_Boosting': GradientBoostingClassifier(n_estimators=100, random_state=42)
        }
        
        # 如果XGBoost可用，添加XGBoost模型
        if XGB_AVAILABLE:
            if dataset_name == 'heart':
                models['XGBoost'] = xgb.XGBClassifier(
                    n_estimators=500, max_depth=4, random_state=42
                )
            else:
                models['XGBoost'] = xgb.XGBClassifier(
                    n_estimators=300, max_depth=6, random_state=42
                )
        
        # 训练和评估模型
        results = {}
        trained_models = {}
        
        for model_name, model in models.items():
            print(f"  训练 {model_name}...")
            
            # 训练模型
            model.fit(X_train, y_train)
            
            # 预测
            y_pred = model.predict(X_test)
            y_pred_proba = model.predict_proba(X_test)[:, 1]
            
            # 计算指标
            metrics = {
                'accuracy': accuracy_score(y_test, y_pred),
                'precision': precision_score(y_test, y_pred, average='weighted'),
                'recall': recall_score(y_test, y_pred, average='weighted'),
                'f1': f1_score(y_test, y_pred, average='weighted'),
                'auc': roc_auc_score(y_test, y_pred_proba)
            }
            
            # 交叉验证
            cv_scores = cross_val_score(model, X_train, y_train, cv=5, scoring='roc_auc')
            metrics['cv_auc_mean'] = cv_scores.mean()
            metrics['cv_auc_std'] = cv_scores.std()
            
            results[model_name] = metrics
            trained_models[model_name] = model
            
            print(f"    AUC: {metrics['auc']:.3f}, F1: {metrics['f1']:.3f}")
        
        # 保存结果
        self.models[dataset_name] = trained_models
        self.results[dataset_name] = {
            'metrics': results,
            'X_test': X_test,
            'y_test': y_test,
            'feature_names': X.columns.tolist()
        }
        
        return results, trained_models
