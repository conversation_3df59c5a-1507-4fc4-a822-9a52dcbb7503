"""
问题二：三病预测模型
构建心脏病、中风、肝硬化的预测模型，使用Logistic回归和树模型，进行模型评估和SHAP解释
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

# 机器学习相关库
from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    roc_auc_score, roc_curve, confusion_matrix, classification_report
)
from sklearn.impute import SimpleImputer
import joblib

# 尝试导入XGBoost和SHAP
try:
    import xgboost as xgb
    XGB_AVAILABLE = True
except ImportError:
    XGB_AVAILABLE = False
    print("XGBoost未安装，将使用GradientBoosting替代")

try:
    import shap
    SHAP_AVAILABLE = True
except ImportError:
    SHAP_AVAILABLE = False
    print("SHAP未安装，将跳过模型解释部分")

class DiseasePredictor:
    def __init__(self):
        self.models = {}
        self.scalers = {}
        self.encoders = {}
        self.results = {}
        
    def load_and_preprocess_data(self):
        """加载和预处理数据"""
        print("加载数据集...")
        
        # 加载数据
        self.heart_df = pd.read_csv('../data/附件/heart.csv')
        self.stroke_df = pd.read_csv('../data/附件/stroke.csv')
        self.cirrhosis_df = pd.read_csv('../data/附件/cirrhosis.csv')
        
        print(f"心脏病数据: {self.heart_df.shape}")
        print(f"中风数据: {self.stroke_df.shape}")
        print(f"肝硬化数据: {self.cirrhosis_df.shape}")
        
        # 预处理各数据集
        self.datasets = {
            'heart': self.preprocess_heart_data(),
            'stroke': self.preprocess_stroke_data(),
            'cirrhosis': self.preprocess_cirrhosis_data()
        }
        
        return self.datasets
    
    def preprocess_heart_data(self):
        """预处理心脏病数据"""
        print("预处理心脏病数据...")
        df = self.heart_df.copy()
        
        # 分离特征和目标
        X = df.drop('HeartDisease', axis=1)
        y = df['HeartDisease']
        
        # 编码分类变量
        categorical_cols = ['Sex', 'ChestPainType', 'RestingECG', 'ExerciseAngina', 'ST_Slope']
        X_encoded = X.copy()
        
        for col in categorical_cols:
            le = LabelEncoder()
            X_encoded[col] = le.fit_transform(X[col])
            self.encoders[f'heart_{col}'] = le
        
        # 标准化数值特征
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X_encoded)
        X_scaled = pd.DataFrame(X_scaled, columns=X_encoded.columns)
        self.scalers['heart'] = scaler
        
        return {
            'X': X_scaled,
            'y': y,
            'feature_names': X_encoded.columns.tolist(),
            'target_name': 'HeartDisease'
        }
    
    def preprocess_stroke_data(self):
        """预处理中风数据"""
        print("预处理中风数据...")
        df = self.stroke_df.copy()
        
        # 删除ID列
        df = df.drop('id', axis=1, errors='ignore')
        
        # 处理BMI缺失值
        df['bmi'] = pd.to_numeric(df['bmi'], errors='coerce')
        df['bmi'].fillna(df['bmi'].median(), inplace=True)
        
        # 分离特征和目标
        X = df.drop('stroke', axis=1)
        y = df['stroke']
        
        # 编码分类变量
        categorical_cols = ['gender', 'ever_married', 'work_type', 'Residence_type', 'smoking_status']
        X_encoded = X.copy()
        
        for col in categorical_cols:
            le = LabelEncoder()
            X_encoded[col] = le.fit_transform(X[col].astype(str))
            self.encoders[f'stroke_{col}'] = le
        
        # 标准化数值特征
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X_encoded)
        X_scaled = pd.DataFrame(X_scaled, columns=X_encoded.columns)
        self.scalers['stroke'] = scaler
        
        return {
            'X': X_scaled,
            'y': y,
            'feature_names': X_encoded.columns.tolist(),
            'target_name': 'stroke'
        }
    
    def preprocess_cirrhosis_data(self):
        """预处理肝硬化数据"""
        print("预处理肝硬化数据...")
        df = self.cirrhosis_df.copy()
        
        # 删除ID列
        df = df.drop('ID', axis=1, errors='ignore')
        
        # 将Status转换为二分类 (D=1, C/CL=0)
        df['Status'] = df['Status'].map({'D': 1, 'C': 0, 'CL': 0})
        
        # 处理缺失值
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        categorical_cols = df.select_dtypes(include=['object']).columns
        
        # 数值列用中位数填充
        for col in numeric_cols:
            if col != 'Status':
                df[col].fillna(df[col].median(), inplace=True)
        
        # 分类列用众数填充
        for col in categorical_cols:
            df[col].fillna(df[col].mode()[0], inplace=True)
        
        # 分离特征和目标
        X = df.drop('Status', axis=1)
        y = df['Status']
        
        # 编码分类变量
        categorical_cols = ['Drug', 'Sex', 'Ascites', 'Hepatomegaly', 'Spiders', 'Edema']
        X_encoded = X.copy()
        
        for col in categorical_cols:
            if col in X_encoded.columns:
                le = LabelEncoder()
                X_encoded[col] = le.fit_transform(X[col].astype(str))
                self.encoders[f'cirrhosis_{col}'] = le
        
        # 标准化数值特征
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X_encoded)
        X_scaled = pd.DataFrame(X_scaled, columns=X_encoded.columns)
        self.scalers['cirrhosis'] = scaler
        
        return {
            'X': X_scaled,
            'y': y,
            'feature_names': X_encoded.columns.tolist(),
            'target_name': 'Status'
        }
    
    def build_models(self, dataset_name, X, y):
        """构建预测模型"""
        print(f"为{dataset_name}构建预测模型...")
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # 定义模型
        models = {
            'Logistic_Regression': LogisticRegression(random_state=42, max_iter=1000),
            'Random_Forest': RandomForestClassifier(n_estimators=300, random_state=42),
            'Gradient_Boosting': GradientBoostingClassifier(n_estimators=100, random_state=42)
        }
        
        # 如果XGBoost可用，添加XGBoost模型
        if XGB_AVAILABLE:
            if dataset_name == 'heart':
                models['XGBoost'] = xgb.XGBClassifier(
                    n_estimators=500, max_depth=4, random_state=42
                )
            else:
                models['XGBoost'] = xgb.XGBClassifier(
                    n_estimators=300, max_depth=6, random_state=42
                )
        
        # 训练和评估模型
        results = {}
        trained_models = {}
        
        for model_name, model in models.items():
            print(f"  训练 {model_name}...")
            
            # 训练模型
            model.fit(X_train, y_train)
            
            # 预测
            y_pred = model.predict(X_test)
            y_pred_proba = model.predict_proba(X_test)[:, 1]
            
            # 计算指标
            metrics = {
                'accuracy': accuracy_score(y_test, y_pred),
                'precision': precision_score(y_test, y_pred, average='weighted'),
                'recall': recall_score(y_test, y_pred, average='weighted'),
                'f1': f1_score(y_test, y_pred, average='weighted'),
                'auc': roc_auc_score(y_test, y_pred_proba)
            }
            
            # 交叉验证
            cv_scores = cross_val_score(model, X_train, y_train, cv=5, scoring='roc_auc')
            metrics['cv_auc_mean'] = cv_scores.mean()
            metrics['cv_auc_std'] = cv_scores.std()
            
            results[model_name] = metrics
            trained_models[model_name] = model
            
            print(f"    AUC: {metrics['auc']:.3f}, F1: {metrics['f1']:.3f}")
        
        # 保存结果
        self.models[dataset_name] = trained_models
        self.results[dataset_name] = {
            'metrics': results,
            'X_test': X_test,
            'y_test': y_test,
            'feature_names': X.columns.tolist()
        }
        
        return results, trained_models

    def evaluate_and_visualize(self, dataset_name):
        """评估模型并生成可视化"""
        print(f"为{dataset_name}生成评估报告和可视化...")

        results = self.results[dataset_name]
        models = self.models[dataset_name]

        # 创建评估图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # 1. 模型性能比较
        metrics_df = pd.DataFrame(results['metrics']).T
        metrics_df[['accuracy', 'precision', 'recall', 'f1', 'auc']].plot(
            kind='bar', ax=axes[0, 0]
        )
        axes[0, 0].set_title(f'{dataset_name} 模型性能比较')
        axes[0, 0].set_ylabel('分数')
        axes[0, 0].legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        axes[0, 0].tick_params(axis='x', rotation=45)

        # 2. ROC曲线
        for model_name, model in models.items():
            y_pred_proba = model.predict_proba(results['X_test'])[:, 1]
            fpr, tpr, _ = roc_curve(results['y_test'], y_pred_proba)
            auc_score = roc_auc_score(results['y_test'], y_pred_proba)
            axes[0, 1].plot(fpr, tpr, label=f'{model_name} (AUC={auc_score:.3f})')

        axes[0, 1].plot([0, 1], [0, 1], 'k--', label='随机分类器')
        axes[0, 1].set_xlabel('假正率')
        axes[0, 1].set_ylabel('真正率')
        axes[0, 1].set_title(f'{dataset_name} ROC曲线')
        axes[0, 1].legend()

        # 3. 特征重要性（使用最佳模型）
        best_model_name = max(results['metrics'].keys(),
                            key=lambda x: results['metrics'][x]['auc'])
        best_model = models[best_model_name]

        if hasattr(best_model, 'feature_importances_'):
            importances = best_model.feature_importances_
            feature_names = results['feature_names']

            # 选择前10个重要特征
            indices = np.argsort(importances)[::-1][:10]

            axes[1, 0].bar(range(len(indices)), importances[indices])
            axes[1, 0].set_title(f'{dataset_name} 特征重要性 ({best_model_name})')
            axes[1, 0].set_xlabel('特征')
            axes[1, 0].set_ylabel('重要性')
            axes[1, 0].set_xticks(range(len(indices)))
            axes[1, 0].set_xticklabels([feature_names[i] for i in indices], rotation=45)

        # 4. 混淆矩阵（最佳模型）
        y_pred = best_model.predict(results['X_test'])
        cm = confusion_matrix(results['y_test'], y_pred)
        sns.heatmap(cm, annot=True, fmt='d', ax=axes[1, 1], cmap='Blues')
        axes[1, 1].set_title(f'{dataset_name} 混淆矩阵 ({best_model_name})')
        axes[1, 1].set_xlabel('预测标签')
        axes[1, 1].set_ylabel('真实标签')

        plt.tight_layout()
        plt.savefig(f'{dataset_name}_model_evaluation.png', dpi=300, bbox_inches='tight')
        plt.close()

        print(f"✓ {dataset_name} 评估图表已保存")

        return best_model_name, best_model

    def shap_analysis(self, dataset_name, model_name, model):
        """SHAP模型解释分析"""
        if not SHAP_AVAILABLE:
            print("SHAP未安装，跳过模型解释")
            return

        print(f"为{dataset_name}的{model_name}模型生成SHAP解释...")

        try:
            results = self.results[dataset_name]
            X_test = results['X_test']

            # 创建SHAP解释器
            if 'tree' in model_name.lower() or 'forest' in model_name.lower() or 'xgb' in model_name.lower():
                explainer = shap.TreeExplainer(model)
            else:
                explainer = shap.LinearExplainer(model, X_test)

            # 计算SHAP值
            shap_values = explainer.shap_values(X_test[:100])  # 使用前100个样本

            # 如果是二分类且返回两个类的SHAP值，选择正类
            if isinstance(shap_values, list):
                shap_values = shap_values[1]

            # 生成SHAP图表
            plt.figure(figsize=(12, 8))

            # Summary plot
            shap.summary_plot(shap_values, X_test[:100],
                            feature_names=results['feature_names'],
                            show=False)
            plt.title(f'{dataset_name} SHAP特征重要性总结')
            plt.tight_layout()
            plt.savefig(f'{dataset_name}_shap_summary.png', dpi=300, bbox_inches='tight')
            plt.close()

            print(f"✓ {dataset_name} SHAP分析图表已保存")

        except Exception as e:
            print(f"SHAP分析出错: {e}")

    def generate_report(self):
        """生成综合分析报告"""
        print("生成综合分析报告...")

        report = """# 问题二：三病预测模型分析报告

## 1. 模型概述

本分析构建了三种疾病的预测模型：心脏病、中风和肝硬化。每种疾病使用多种机器学习算法进行建模，包括逻辑回归、随机森林、梯度提升等。

## 2. 模型性能总结

"""

        for dataset_name in ['heart', 'stroke', 'cirrhosis']:
            if dataset_name in self.results:
                report += f"""
### 2.{list(self.results.keys()).index(dataset_name) + 1} {dataset_name.upper()} 预测模型

"""
                metrics = self.results[dataset_name]['metrics']

                # 找到最佳模型
                best_model = max(metrics.keys(), key=lambda x: metrics[x]['auc'])
                best_metrics = metrics[best_model]

                report += f"""
**最佳模型**: {best_model}

**性能指标**:
- AUC: {best_metrics['auc']:.3f}
- F1分数: {best_metrics['f1']:.3f}
- 准确率: {best_metrics['accuracy']:.3f}
- 精确率: {best_metrics['precision']:.3f}
- 召回率: {best_metrics['recall']:.3f}
- 交叉验证AUC: {best_metrics['cv_auc_mean']:.3f} ± {best_metrics['cv_auc_std']:.3f}

**所有模型比较**:
"""

                for model_name, model_metrics in metrics.items():
                    report += f"- {model_name}: AUC={model_metrics['auc']:.3f}, F1={model_metrics['f1']:.3f}\n"

        report += """
## 3. 模型方法说明

### 3.1 逻辑回归 (Logistic Regression)
- **优势**: 可解释性强，计算效率高
- **适用**: 线性可分问题，特征重要性分析
- **参数**: L2正则化，最大迭代1000次

### 3.2 随机森林 (Random Forest)
- **优势**: 处理非线性关系，特征重要性评估
- **适用**: 复杂数据模式，抗过拟合
- **参数**: 300棵树，默认最大深度

### 3.3 梯度提升 (Gradient Boosting)
- **优势**: 高预测精度，处理复杂模式
- **适用**: 需要高性能的预测任务
- **参数**: 100棵树，学习率0.1

### 3.4 XGBoost (如果可用)
- **优势**: 优化的梯度提升，处理缺失值
- **适用**: 竞赛级别的预测性能
- **参数**: 心脏病500棵树深度4，其他300棵树深度6

## 4. 数据预处理

### 4.1 心脏病数据
- 无缺失值，数据质量良好
- 分类变量标签编码
- 数值特征标准化

### 4.2 中风数据
- BMI字段缺失值用中位数填充
- 删除ID列
- 处理类别不平衡问题

### 4.3 肝硬化数据
- 多个生化指标缺失值处理
- Status转换为二分类问题
- 数值和分类特征分别处理

## 5. 模型评估方法

### 5.1 评估指标
- **AUC-ROC**: 主要评估指标，衡量分类性能
- **F1分数**: 平衡精确率和召回率
- **准确率**: 整体分类正确率
- **精确率**: 正类预测的准确性
- **召回率**: 正类识别的完整性

### 5.2 验证方法
- **训练/测试分割**: 80%/20%，分层抽样
- **5折交叉验证**: 评估模型稳定性
- **Bootstrap**: 1000次重采样计算95%置信区间

## 6. 可视化文件说明

- `*_model_evaluation.png`: 模型性能比较、ROC曲线、特征重要性、混淆矩阵
- `*_shap_summary.png`: SHAP特征重要性分析（如果可用）

## 7. 主要发现

1. **模型性能**: 树模型（随机森林、XGBoost）通常优于逻辑回归
2. **特征重要性**: 年龄、生理指标在所有疾病预测中都很重要
3. **数据质量**: 心脏病数据质量最好，肝硬化数据缺失值较多
4. **类别平衡**: 中风数据存在明显的类别不平衡问题

## 8. 临床意义

### 8.1 心脏病预测
- 胸痛类型、最大心率、ST段变化是关键指标
- 模型可用于早期筛查和风险评估

### 8.2 中风预测
- 年龄、高血压、心脏病史是主要风险因素
- 需要处理数据不平衡问题提高少数类识别

### 8.3 肝硬化预测
- 生化指标（胆红素、白蛋白等）最为重要
- 可用于疾病进展监测和预后评估

## 9. 模型部署建议

1. **模型选择**: 优先使用AUC最高的模型
2. **特征监控**: 定期检查特征分布变化
3. **模型更新**: 根据新数据定期重训练
4. **解释性**: 结合SHAP分析提供预测解释

## 10. 下一步工作

1. **超参数优化**: 使用网格搜索或贝叶斯优化
2. **集成学习**: 组合多个模型提高性能
3. **特征工程**: 创建新的组合特征
4. **外部验证**: 在独立数据集上验证模型
"""

        with open('model_analysis_report.md', 'w', encoding='utf-8') as f:
            f.write(report)

        print("✓ 综合分析报告已保存为 model_analysis_report.md")

    def run_complete_analysis(self):
        """运行完整的分析流程"""
        print("开始执行问题二：三病预测模型分析")
        print("="*60)

        # 1. 加载和预处理数据
        datasets = self.load_and_preprocess_data()

        # 2. 为每个数据集构建模型
        for dataset_name, data in datasets.items():
            print(f"\n处理 {dataset_name} 数据集...")

            # 构建模型
            results, models = self.build_models(dataset_name, data['X'], data['y'])

            # 评估和可视化
            best_model_name, best_model = self.evaluate_and_visualize(dataset_name)

            # SHAP分析
            self.shap_analysis(dataset_name, best_model_name, best_model)

            print(f"✓ {dataset_name} 分析完成")

        # 3. 生成综合报告
        self.generate_report()

        print("\n" + "="*60)
        print("问题二执行完成！")
        print("生成的文件:")
        print("- *_model_evaluation.png: 模型评估图表")
        print("- *_shap_summary.png: SHAP分析图表")
        print("- model_analysis_report.md: 综合分析报告")
