"""
问题二主执行脚本
运行三病预测模型分析
"""

import os
import sys
import subprocess

def install_package(package_name):
    """安装缺失的包"""
    try:
        __import__(package_name)
        print(f"✓ {package_name} 已安装")
    except ImportError:
        print(f"× {package_name} 未安装，正在安装...")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package_name, 
                "-i", "https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple/"
            ])
            print(f"✓ {package_name} 安装成功")
        except subprocess.CalledProcessError:
            print(f"× {package_name} 安装失败")
            return False
    return True

def check_and_install_dependencies():
    """检查并安装依赖包"""
    print("检查并安装依赖包...")
    
    # 基础包
    packages = [
        'pandas', 'numpy', 'matplotlib', 'seaborn', 
        'scikit-learn', 'joblib'
    ]
    
    # 可选包
    optional_packages = {
        'xgboost': 'xgboost',
        'shap': 'shap'
    }
    
    # 安装基础包
    for package in packages:
        if not install_package(package):
            print(f"警告: {package} 安装失败，可能影响部分功能")
    
    # 安装可选包
    for pip_name, import_name in optional_packages.items():
        try:
            __import__(import_name)
            print(f"✓ {pip_name} 已安装")
        except ImportError:
            print(f"× {pip_name} 未安装，正在安装...")
            try:
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", pip_name,
                    "-i", "https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple/"
                ])
                print(f"✓ {pip_name} 安装成功")
            except subprocess.CalledProcessError:
                print(f"× {pip_name} 安装失败，将使用替代方案")

# 检查并安装依赖
check_and_install_dependencies()

# 现在导入所需模块
from disease_prediction import DiseasePredictor

def main():
    """主函数"""
    try:
        # 创建预测器实例
        predictor = DiseasePredictor()
        
        # 运行完整分析
        predictor.run_complete_analysis()
        
        print("\n问题二分析成功完成！")
        
    except Exception as e:
        print(f"执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
