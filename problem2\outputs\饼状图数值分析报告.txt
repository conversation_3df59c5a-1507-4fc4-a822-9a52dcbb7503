
# 饼状图数值较低的原因分析报告

## 一、数值低的根本原因

### 1.1 中风预测的特殊挑战
- **极低患病率**: 中风患者仅占4.9% (249/5110)
- **样本严重不平衡**: 正负样本比例约为1:19
- **预测难度**: 中风是所有疾病中最难预测的

### 1.2 "准确率悖论"现象
```
准确率 = 96.5% ✓ (看起来很好)
精确率 = 29.8% ✗ (实际很低)
召回率 = 35.6% ✗ (实际很低)
F1分数 = 32.5% ✗ (综合表现差)
```

**原因**: 模型倾向于预测"无中风"，因为这样准确率最高！

## 二、各疾病预测难度对比

### 2.1 患病率对比
| 疾病 | 患病率 | 预测难度 | 最佳F1分数 |
|------|--------|----------|------------|
| 心脏病 | 55.3% | 低 | 0.901 |
| 肝硬化 | 20.0% | 中等 | 0.787 |
| **中风** | **4.9%** | **极高** | **0.325** |

### 2.2 性能表现分析
- **心脏病**: 样本平衡，特征明显，预测性能优秀
- **肝硬化**: 样本略不平衡，预测性能良好  
- **中风**: 样本极度不平衡，预测性能受限

## 三、为什么饼状图显示数值低？

### 3.1 饼状图显示的是原始分数
- 精确率: 0.298 = 29.8%
- 召回率: 0.356 = 35.6%
- F1分数: 0.325 = 32.5%

### 3.2 这些数值在中风预测中属于合理范围
- **医学文献**: 中风预测F1分数通常在0.2-0.4之间
- **临床现实**: 中风发病具有突发性和复杂性
- **数据限制**: 现有特征无法完全捕获中风风险

## 四、数值低不等于模型差

### 4.1 AUC仍然不错
- 中风预测AUC = 0.871 (87.1%)
- 说明模型有一定的区分能力
- 在不平衡数据中，AUC比F1更可靠

### 4.2 临床价值依然存在
- **筛查工具**: 可用于高危人群初步筛查
- **风险评估**: 提供中风风险的量化评估
- **辅助诊断**: 结合临床经验使用

## 五、改进策略

### 5.1 数据改进
1. **增加中风样本**: 收集更多阳性案例
2. **特征工程**: 添加更多相关特征
3. **数据平衡**: 使用SMOTE等技术

### 5.2 模型改进  
1. **阈值调优**: 降低分类阈值提高召回率
2. **代价敏感**: 对漏诊设置更高代价
3. **集成方法**: 结合多个模型的预测

### 5.3 评估改进
1. **关注AUC**: 而非准确率或F1
2. **PR曲线**: 更适合不平衡数据
3. **临床指标**: 结合医学专业评估

## 六、结论

### 6.1 数值低的合理性
- 中风预测本身就是医学难题
- 当前性能在合理范围内
- 不应简单以数值高低判断模型好坏

### 6.2 实际应用价值
- 可用于高危人群筛查
- 提供风险量化评估
- 辅助临床决策制定

### 6.3 持续改进方向
- 数据质量和数量提升
- 模型算法优化
- 评估方法改进

---
**重要提醒**: 
在不平衡数据的机器学习任务中，低的精确率和召回率是常见现象，
不应仅凭数值大小判断模型质量，需要结合具体应用场景和领域知识进行综合评估。
