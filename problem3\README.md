# 问题三：多疾病关联分析

## 1. 项目概述

本项目通过Apriori关联规则挖掘和贝叶斯网络分析，评估心脏病、中风和肝硬化之间的关联关系，量化共病风险，为综合防控策略提供科学依据。

## 2. 分析方法

### 2.1 Apriori关联规则挖掘
- **算法**: Apriori频繁项集挖掘
- **参数**: 最小支持度1%，最小Lift值1.2
- **评估指标**: Support（支持度）、Confidence（置信度）、Lift（提升度）
- **目标**: 发现疾病间和风险因子与疾病间的关联模式

### 2.2 贝叶斯网络分析
- **结构学习**: Hill-Climbing算法
- **参数估计**: 最大似然估计
- **推断引擎**: Variable Elimination
- **目标**: 建模变量间的因果关系和条件概率

### 2.3 简化概率分析
- **单病概率**: 各疾病的独立发生概率
- **两病共病**: 任意两种疾病的联合概率
- **三病共病**: 三种疾病同时发生的概率
- **条件概率**: 给定风险因子下的疾病概率

## 3. 数据构建

### 3.1 模拟患者数据集
- **样本数量**: 1000名虚拟患者
- **基本信息**: 年龄、性别、患者ID
- **风险因子**: 高血压、糖尿病、吸烟、肥胖
- **疾病状态**: 心脏病、中风、肝硬化

### 3.2 风险模型设计
基于医学文献和流行病学证据，设计疾病发生的概率模型：

**心脏病风险** = 基础风险 + 年龄因子 + 高血压 + 糖尿病 + 吸烟 + 肥胖 + 性别
**中风风险** = 基础风险 + 年龄因子 + 高血压 + 心脏病史 + 糖尿病 + 吸烟
**肝硬化风险** = 基础风险 + 年龄因子 + 吸烟 + 糖尿病 + 肥胖

### 3.3 数据特征
- **心脏病患病率**: ~15%
- **中风患病率**: ~8%
- **肝硬化患病率**: ~5%
- **风险因子分布**: 符合人群流行病学特征

## 4. 关联规则发现

### 4.1 疾病间关联
- **心脏病 → 中风**: 发现心脏病患者中风风险显著增加
- **高血压 → 心脏病**: 高血压是心脏病的强预测因子
- **吸烟 → 肝硬化**: 吸烟与肝硬化存在强关联

### 4.2 风险因子关联
- **年龄 + 高血压 → 心脏病**: 多重风险因子的累积效应
- **糖尿病 + 肥胖 → 中风**: 代谢综合征的疾病风险
- **吸烟 + 年龄 → 肝硬化**: 生活方式与年龄的交互作用

### 4.3 规则评估
- **高置信度规则**: Confidence > 0.3
- **强关联规则**: Lift > 2.0
- **临床意义**: 结合医学知识验证规则合理性

## 5. 共病概率分析

### 5.1 单病概率
- 各疾病的边际概率分布
- 不同人群的患病率差异
- 年龄和性别分层分析

### 5.2 两病共病
- **心脏病 + 中风**: 心血管疾病共病模式
- **心脏病 + 肝硬化**: 系统性疾病关联
- **中风 + 肝硬化**: 相对独立的疾病组合

### 5.3 三病共病
- 三种疾病同时发生的极低概率
- 超高风险患者的识别
- 综合管理的必要性

## 6. 可视化分析

### 6.1 疾病共现热图 (disease_cooccurrence_heatmap.png)
- **矩阵形式**: 展示疾病间的共现概率
- **颜色编码**: 深色表示高共现率
- **对角线**: 单病患病率
- **非对角线**: 共病概率

### 6.2 关联规则图表 (association_rules_lift.png)
- **柱状图**: 按Lift值排序的关联规则
- **规则标签**: 前因 → 后果的规则表示
- **强度指示**: Lift值越高，关联越强
- **临床解释**: 结合医学知识解读规则

### 6.3 共病概率分布 (comorbidity_probabilities.png)
- **概率谱**: 从单病到多病的概率分布
- **数值标注**: 精确的概率值标记
- **趋势分析**: 共病概率的递减模式
- **风险分层**: 不同风险水平的患者分布

### 6.4 Sankey流图 (sankey_diagram.png)
- **流向可视化**: 风险因子到疾病的关联流
- **流量大小**: 表示关联强度
- **多层结构**: 风险因子 → 疾病的层次关系
- **交互探索**: 可交互的流向分析

## 7. 贝叶斯网络建模

### 7.1 网络结构
- **节点**: 疾病和风险因子变量
- **边**: 变量间的依赖关系
- **方向**: 因果关系的方向性
- **强度**: 条件概率的大小

### 7.2 参数学习
- **条件概率表**: 每个节点的CPT
- **最大似然估计**: 基于数据的参数估计
- **平滑处理**: 避免零概率问题
- **验证**: 模型拟合度检验

### 7.3 概率推断
- **边际概率**: 单个变量的概率分布
- **条件概率**: 给定证据下的后验概率
- **联合概率**: 多个变量的联合分布
- **敏感性分析**: 参数变化对结果的影响

## 8. 技术实现

### 8.1 核心类: ComorbidityAnalyzer
```python
class ComorbidityAnalyzer:
    - load_and_combine_data(): 加载并合并数据集
    - create_combined_dataset(): 创建模拟患者数据
    - apriori_analysis(): Apriori关联规则挖掘
    - simple_association_analysis(): 简化关联分析
    - bayesian_network_analysis(): 贝叶斯网络分析
    - simple_probability_analysis(): 简化概率分析
    - create_visualizations(): 创建可视化图表
    - create_sankey_diagram(): 创建Sankey流图
    - generate_report(): 生成分析报告
```

### 8.2 依赖库管理
- **mlxtend**: Apriori算法实现
- **pgmpy**: 贝叶斯网络建模
- **plotly**: 交互式可视化
- **networkx**: 网络图分析
- **优雅降级**: 缺失库时的替代方案

## 9. 临床意义

### 9.1 共病风险识别
- **高危人群**: 多重风险因子患者的识别
- **风险分层**: 基于共病概率的患者分层
- **预警系统**: 早期识别共病风险
- **个性化评估**: 基于个体特征的风险评估

### 9.2 防控策略指导
- **综合干预**: 针对多种疾病的综合防控
- **优先级排序**: 基于关联强度的干预优先级
- **资源配置**: 医疗资源的合理分配
- **效果评估**: 干预措施的效果监测

### 9.3 临床决策支持
- **诊断辅助**: 基于症状和风险因子的诊断支持
- **治疗规划**: 考虑共病风险的治疗方案
- **随访管理**: 高风险患者的长期管理
- **预后评估**: 基于共病模式的预后预测

## 10. 文件结构
```
problem3/
├── comorbidity_analysis.py     # 核心分析类
├── main.py                    # 主执行脚本
├── README.md                  # 本文档
└── 输出文件/
    ├── disease_cooccurrence_heatmap.png
    ├── association_rules_lift.png
    ├── comorbidity_probabilities.png
    ├── sankey_diagram.png
    └── comorbidity_analysis_report.md
```

## 11. 使用方法

### 11.1 环境准备
```bash
conda activate play
cd problem3
```

### 11.2 运行分析
```bash
python main.py
```

### 11.3 结果解读
- 查看可视化图表了解关联模式
- 阅读分析报告获取详细发现
- 结合临床知识验证分析结果

## 12. 方法学优势

### 12.1 Apriori算法
- **无监督学习**: 不需要预定义的目标变量
- **模式发现**: 自动发现隐藏的关联模式
- **可解释性**: 规则形式易于理解和解释
- **临床适用**: 符合临床思维的逻辑推理

### 12.2 贝叶斯网络
- **因果建模**: 能够表示变量间的因果关系
- **不确定性**: 处理医学诊断中的不确定性
- **推理能力**: 支持多种类型的概率推理
- **知识融合**: 结合先验知识和数据证据

## 13. 局限性与改进

### 13.1 数据局限性
- **模拟数据**: 基于假设的模拟数据，非真实临床数据
- **样本量**: 相对较小的样本量可能影响结果稳定性
- **时间维度**: 缺乏疾病发展的时间序列信息
- **混杂因子**: 未考虑所有可能的混杂变量

### 13.2 方法局限性
- **因果推断**: 关联不等于因果，需要谨慎解释
- **模型假设**: 贝叶斯网络的条件独立假设可能不成立
- **参数敏感性**: 结果可能对参数设置敏感
- **外部效度**: 模拟数据的外部效度有限

### 13.3 改进方向
- **真实数据**: 使用大规模真实临床数据验证
- **纵向研究**: 引入时间序列分析方法
- **深度学习**: 结合深度学习方法挖掘复杂模式
- **多中心验证**: 在不同人群中验证发现的关联

## 14. 参考文献

1. Agrawal, R., & Srikant, R. (1994). Fast algorithms for mining association rules
2. Pearl, J. (2009). Causality: models, reasoning and inference
3. Koller, D., & Friedman, N. (2009). Probabilistic graphical models
4. Tan, P. N., et al. (2005). Introduction to data mining
