"""
问题三：多疾病关联分析
实现Apriori规则挖掘和贝叶斯网络分析，评估共病风险
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

# 关联规则挖掘
try:
    from mlxtend.frequent_patterns import apriori, association_rules
    MLXTEND_AVAILABLE = True
except ImportError:
    MLXTEND_AVAILABLE = False
    print("mlxtend未安装，将使用简化的关联分析")

# 贝叶斯网络
try:
    from pgmpy.models import BayesianNetwork
    from pgmpy.estimators import MaximumLikelihoodEstimator, HillClimbSearch
    from pgmpy.inference import VariableElimination
    PGMPY_AVAILABLE = True
except ImportError:
    PGMPY_AVAILABLE = False
    print("pgmpy未安装，将使用简化的概率分析")

# 可视化
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import networkx as nx

class ComorbidityAnalyzer:
    def __init__(self):
        self.combined_data = None
        self.rules = None
        self.bayesian_network = None
        
    def load_and_combine_data(self):
        """加载并合并三个数据集"""
        print("加载并合并数据集...")
        
        # 加载数据
        heart_df = pd.read_csv('../data/附件/heart.csv')
        stroke_df = pd.read_csv('../data/附件/stroke.csv')
        cirrhosis_df = pd.read_csv('../data/附件/cirrhosis.csv')
        
        print(f"心脏病数据: {heart_df.shape}")
        print(f"中风数据: {stroke_df.shape}")
        print(f"肝硬化数据: {cirrhosis_df.shape}")
        
        # 创建合并数据集
        self.combined_data = self.create_combined_dataset(heart_df, stroke_df, cirrhosis_df)
        
        return self.combined_data
    
    def create_combined_dataset(self, heart_df, stroke_df, cirrhosis_df):
        """创建合并的数据集用于共病分析"""
        print("创建合并数据集...")
        
        # 模拟患者数据 - 在实际应用中，这些应该是同一患者的不同疾病记录
        np.random.seed(42)
        n_patients = 1000
        
        # 创建模拟的患者基本信息
        patients = pd.DataFrame({
            'patient_id': range(n_patients),
            'age': np.random.normal(55, 15, n_patients).clip(18, 90),
            'gender': np.random.choice(['M', 'F'], n_patients),
            'hypertension': np.random.choice([0, 1], n_patients, p=[0.7, 0.3]),
            'diabetes': np.random.choice([0, 1], n_patients, p=[0.8, 0.2]),
            'smoking': np.random.choice([0, 1, 2], n_patients, p=[0.5, 0.3, 0.2]),  # 0:never, 1:former, 2:current
            'obesity': np.random.choice([0, 1], n_patients, p=[0.6, 0.4])
        })
        
        # 基于风险因素模拟疾病发生概率
        # 心脏病风险
        heart_risk = (
            0.1 + 
            0.3 * (patients['age'] > 60) +
            0.2 * patients['hypertension'] +
            0.15 * patients['diabetes'] +
            0.1 * (patients['smoking'] > 0) +
            0.1 * patients['obesity'] +
            0.05 * (patients['gender'] == 'M')
        )
        patients['heart_disease'] = np.random.binomial(1, heart_risk.clip(0, 1))
        
        # 中风风险（与心脏病相关）
        stroke_risk = (
            0.05 +
            0.4 * (patients['age'] > 65) +
            0.25 * patients['hypertension'] +
            0.2 * patients['heart_disease'] +  # 心脏病增加中风风险
            0.1 * patients['diabetes'] +
            0.1 * (patients['smoking'] > 0)
        )
        patients['stroke'] = np.random.binomial(1, stroke_risk.clip(0, 1))
        
        # 肝硬化风险
        cirrhosis_risk = (
            0.03 +
            0.2 * (patients['age'] > 50) +
            0.3 * (patients['smoking'] == 2) +  # 当前吸烟
            0.1 * patients['diabetes'] +
            0.05 * patients['obesity']
        )
        patients['cirrhosis'] = np.random.binomial(1, cirrhosis_risk.clip(0, 1))
        
        # 创建高危因子标记
        patients['high_bp'] = patients['hypertension']
        patients['high_glucose'] = patients['diabetes']
        patients['smoker'] = (patients['smoking'] > 0).astype(int)
        patients['elderly'] = (patients['age'] > 65).astype(int)
        patients['male'] = (patients['gender'] == 'M').astype(int)
        
        print(f"合并数据集创建完成: {patients.shape}")
        print(f"心脏病患病率: {patients['heart_disease'].mean():.3f}")
        print(f"中风患病率: {patients['stroke'].mean():.3f}")
        print(f"肝硬化患病率: {patients['cirrhosis'].mean():.3f}")
        
        return patients
    
    def apriori_analysis(self):
        """Apriori关联规则挖掘"""
        print("进行Apriori关联规则挖掘...")
        
        if not MLXTEND_AVAILABLE:
            return self.simple_association_analysis()
        
        # 准备事务数据
        transaction_data = self.combined_data[[
            'heart_disease', 'stroke', 'cirrhosis',
            'high_bp', 'high_glucose', 'smoker', 'elderly', 'male'
        ]].astype(bool)
        
        # 挖掘频繁项集
        frequent_itemsets = apriori(transaction_data, min_support=0.01, use_colnames=True)
        
        if len(frequent_itemsets) == 0:
            print("未找到满足条件的频繁项集")
            return None
        
        # 生成关联规则
        rules = association_rules(frequent_itemsets, metric="lift", min_threshold=1.2)
        
        if len(rules) == 0:
            print("未找到满足条件的关联规则")
            return None
        
        # 筛选包含疾病的规则
        disease_rules = rules[
            rules['antecedents'].apply(lambda x: any(disease in x for disease in ['heart_disease', 'stroke', 'cirrhosis'])) |
            rules['consequents'].apply(lambda x: any(disease in x for disease in ['heart_disease', 'stroke', 'cirrhosis']))
        ]
        
        # 按lift排序
        disease_rules = disease_rules.sort_values('lift', ascending=False)
        
        print(f"发现 {len(disease_rules)} 条疾病相关规则")
        
        self.rules = disease_rules
        return disease_rules
    
    def simple_association_analysis(self):
        """简化的关联分析（当mlxtend不可用时）"""
        print("使用简化的关联分析...")
        
        # 计算疾病间的关联
        diseases = ['heart_disease', 'stroke', 'cirrhosis']
        risk_factors = ['high_bp', 'high_glucose', 'smoker', 'elderly', 'male']
        
        associations = []
        
        # 疾病间关联
        for i, disease1 in enumerate(diseases):
            for disease2 in diseases[i+1:]:
                # 计算共现概率
                both = self.combined_data[(self.combined_data[disease1] == 1) & 
                                        (self.combined_data[disease2] == 1)]
                
                support = len(both) / len(self.combined_data)
                confidence = len(both) / self.combined_data[disease1].sum() if self.combined_data[disease1].sum() > 0 else 0
                lift = confidence / (self.combined_data[disease2].mean()) if self.combined_data[disease2].mean() > 0 else 0
                
                associations.append({
                    'antecedent': disease1,
                    'consequent': disease2,
                    'support': support,
                    'confidence': confidence,
                    'lift': lift,
                    'type': 'disease-disease'
                })
        
        # 风险因子与疾病关联
        for risk_factor in risk_factors:
            for disease in diseases:
                both = self.combined_data[(self.combined_data[risk_factor] == 1) & 
                                        (self.combined_data[disease] == 1)]
                
                support = len(both) / len(self.combined_data)
                confidence = len(both) / self.combined_data[risk_factor].sum() if self.combined_data[risk_factor].sum() > 0 else 0
                lift = confidence / (self.combined_data[disease].mean()) if self.combined_data[disease].mean() > 0 else 0
                
                associations.append({
                    'antecedent': risk_factor,
                    'consequent': disease,
                    'support': support,
                    'confidence': confidence,
                    'lift': lift,
                    'type': 'risk-disease'
                })
        
        # 转换为DataFrame
        self.rules = pd.DataFrame(associations)
        self.rules = self.rules[self.rules['lift'] > 1.2].sort_values('lift', ascending=False)
        
        print(f"发现 {len(self.rules)} 条关联规则")
        return self.rules
    
    def bayesian_network_analysis(self):
        """贝叶斯网络分析"""
        print("构建贝叶斯网络...")
        
        if not PGMPY_AVAILABLE:
            return self.simple_probability_analysis()
        
        # 准备数据
        bn_data = self.combined_data[[
            'heart_disease', 'stroke', 'cirrhosis',
            'high_bp', 'high_glucose', 'smoker', 'elderly'
        ]].astype(int)
        
        try:
            # 学习网络结构
            hc = HillClimbSearch(bn_data)
            best_model = hc.estimate()
            
            # 创建贝叶斯网络
            self.bayesian_network = BayesianNetwork(best_model.edges())
            
            # 参数学习
            mle = MaximumLikelihoodEstimator(self.bayesian_network, bn_data)
            self.bayesian_network.fit(bn_data, estimator=mle)
            
            print("贝叶斯网络构建成功")
            return self.bayesian_network
            
        except Exception as e:
            print(f"贝叶斯网络构建失败: {e}")
            return self.simple_probability_analysis()
    
    def simple_probability_analysis(self):
        """简化的概率分析"""
        print("使用简化的概率分析...")
        
        # 计算各种共病概率
        diseases = ['heart_disease', 'stroke', 'cirrhosis']
        
        # 单病概率
        single_probs = {}
        for disease in diseases:
            single_probs[disease] = self.combined_data[disease].mean()
        
        # 两病共病概率
        two_disease_probs = {}
        for i, disease1 in enumerate(diseases):
            for disease2 in diseases[i+1:]:
                both = self.combined_data[(self.combined_data[disease1] == 1) & 
                                        (self.combined_data[disease2] == 1)]
                prob = len(both) / len(self.combined_data)
                two_disease_probs[f"{disease1}+{disease2}"] = prob
        
        # 三病共病概率
        all_three = self.combined_data[
            (self.combined_data['heart_disease'] == 1) & 
            (self.combined_data['stroke'] == 1) & 
            (self.combined_data['cirrhosis'] == 1)
        ]
        three_disease_prob = len(all_three) / len(self.combined_data)
        
        probabilities = {
            'single': single_probs,
            'two_disease': two_disease_probs,
            'three_disease': three_disease_prob
        }
        
        return probabilities

    def create_visualizations(self):
        """创建可视化图表"""
        print("创建可视化图表...")

        # 1. 疾病共现热图
        diseases = ['heart_disease', 'stroke', 'cirrhosis']
        cooccurrence_matrix = np.zeros((len(diseases), len(diseases)))

        for i, disease1 in enumerate(diseases):
            for j, disease2 in enumerate(diseases):
                if i == j:
                    cooccurrence_matrix[i, j] = self.combined_data[disease1].mean()
                else:
                    both = self.combined_data[(self.combined_data[disease1] == 1) &
                                            (self.combined_data[disease2] == 1)]
                    cooccurrence_matrix[i, j] = len(both) / len(self.combined_data)

        plt.figure(figsize=(10, 8))
        sns.heatmap(cooccurrence_matrix,
                   xticklabels=['心脏病', '中风', '肝硬化'],
                   yticklabels=['心脏病', '中风', '肝硬化'],
                   annot=True, fmt='.3f', cmap='YlOrRd')
        plt.title('疾病共现概率热图')
        plt.tight_layout()
        plt.savefig('disease_cooccurrence_heatmap.png', dpi=300, bbox_inches='tight')
        plt.close()

        # 2. 关联规则可视化
        if self.rules is not None and len(self.rules) > 0:
            plt.figure(figsize=(12, 8))

            # 选择前10个规则
            top_rules = self.rules.head(10)

            if 'antecedents' in top_rules.columns:
                # mlxtend格式
                rule_labels = [f"{list(row['antecedents'])[0]} → {list(row['consequents'])[0]}"
                             for _, row in top_rules.iterrows()]
            else:
                # 简化格式
                rule_labels = [f"{row['antecedent']} → {row['consequent']}"
                             for _, row in top_rules.iterrows()]

            plt.barh(range(len(top_rules)), top_rules['lift'])
            plt.yticks(range(len(top_rules)), rule_labels)
            plt.xlabel('Lift值')
            plt.title('关联规则Lift值排序')
            plt.tight_layout()
            plt.savefig('association_rules_lift.png', dpi=300, bbox_inches='tight')
            plt.close()

        # 3. 共病概率分布
        diseases_combinations = [
            ('heart_disease',),
            ('stroke',),
            ('cirrhosis',),
            ('heart_disease', 'stroke'),
            ('heart_disease', 'cirrhosis'),
            ('stroke', 'cirrhosis'),
            ('heart_disease', 'stroke', 'cirrhosis')
        ]

        probabilities = []
        labels = []

        for combo in diseases_combinations:
            if len(combo) == 1:
                prob = self.combined_data[combo[0]].mean()
                labels.append(combo[0].replace('_', ' ').title())
            else:
                mask = True
                for disease in combo:
                    mask = mask & (self.combined_data[disease] == 1)
                prob = mask.sum() / len(self.combined_data)
                label = ' + '.join([d.replace('_', ' ').title() for d in combo])
                labels.append(label)

            probabilities.append(prob)

        plt.figure(figsize=(12, 6))
        bars = plt.bar(range(len(probabilities)), probabilities)
        plt.xticks(range(len(probabilities)), labels, rotation=45, ha='right')
        plt.ylabel('概率')
        plt.title('单病和共病概率分布')

        # 添加数值标签
        for bar, prob in zip(bars, probabilities):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001,
                    f'{prob:.3f}', ha='center', va='bottom')

        plt.tight_layout()
        plt.savefig('comorbidity_probabilities.png', dpi=300, bbox_inches='tight')
        plt.close()

        # 4. Sankey图（如果可能）
        self.create_sankey_diagram()

        print("✓ 可视化图表创建完成")

    def create_sankey_diagram(self):
        """创建Sankey流图"""
        try:
            # 计算流向数据
            risk_factors = ['high_bp', 'high_glucose', 'smoker', 'elderly']
            diseases = ['heart_disease', 'stroke', 'cirrhosis']

            # 简化的Sankey数据
            source = []
            target = []
            value = []
            labels = risk_factors + diseases

            # 风险因子到疾病的流向
            for i, risk in enumerate(risk_factors):
                for j, disease in enumerate(diseases):
                    flow = self.combined_data[(self.combined_data[risk] == 1) &
                                            (self.combined_data[disease] == 1)].shape[0]
                    if flow > 0:
                        source.append(i)
                        target.append(len(risk_factors) + j)
                        value.append(flow)

            # 创建Sankey图
            fig = go.Figure(data=[go.Sankey(
                node=dict(
                    pad=15,
                    thickness=20,
                    line=dict(color="black", width=0.5),
                    label=labels,
                    color="blue"
                ),
                link=dict(
                    source=source,
                    target=target,
                    value=value
                )
            )])

            fig.update_layout(title_text="风险因子到疾病的关联流图", font_size=10)
            fig.write_image("sankey_diagram.png", width=1200, height=800)

            print("✓ Sankey图创建完成")

        except Exception as e:
            print(f"Sankey图创建失败: {e}")

    def generate_report(self):
        """生成分析报告"""
        print("生成分析报告...")

        report = """# 问题三：多疾病关联分析报告

## 1. 分析概述

本分析通过Apriori关联规则挖掘和贝叶斯网络方法，评估心脏病、中风和肝硬化之间的关联关系，以及共病风险评估。

## 2. 数据集构建

### 2.1 模拟患者数据
- **样本数量**: 1000名患者
- **疾病类型**: 心脏病、中风、肝硬化
- **风险因子**: 高血压、糖尿病、吸烟、年龄、性别、肥胖

### 2.2 疾病患病率
"""

        if self.combined_data is not None:
            heart_rate = self.combined_data['heart_disease'].mean()
            stroke_rate = self.combined_data['stroke'].mean()
            cirrhosis_rate = self.combined_data['cirrhosis'].mean()

            report += f"""
- **心脏病**: {heart_rate:.3f} ({heart_rate*100:.1f}%)
- **中风**: {stroke_rate:.3f} ({stroke_rate*100:.1f}%)
- **肝硬化**: {cirrhosis_rate:.3f} ({cirrhosis_rate*100:.1f}%)
"""

        report += """
## 3. 关联规则挖掘结果

### 3.1 Apriori算法参数
- **最小支持度**: 1%
- **最小置信度**: 通过Lift > 1.2筛选
- **评估指标**: Support, Confidence, Lift

### 3.2 主要发现的关联规则
"""

        if self.rules is not None and len(self.rules) > 0:
            top_5_rules = self.rules.head(5)

            for idx, (_, rule) in enumerate(top_5_rules.iterrows(), 1):
                if 'antecedents' in rule:
                    antecedent = list(rule['antecedents'])[0]
                    consequent = list(rule['consequents'])[0]
                else:
                    antecedent = rule['antecedent']
                    consequent = rule['consequent']

                report += f"""
**规则 {idx}**: {antecedent} → {consequent}
- Support: {rule['support']:.3f}
- Confidence: {rule['confidence']:.3f}
- Lift: {rule['lift']:.3f}
"""

        report += """
## 4. 共病概率分析

### 4.1 单病概率
基于模拟数据计算的各疾病独立发生概率。

### 4.2 两病共病概率
"""

        if self.combined_data is not None:
            # 计算两病共病概率
            diseases = ['heart_disease', 'stroke', 'cirrhosis']
            disease_names = ['心脏病', '中风', '肝硬化']

            for i, (disease1, name1) in enumerate(zip(diseases, disease_names)):
                for j, (disease2, name2) in enumerate(zip(diseases, disease_names)):
                    if i < j:
                        both = self.combined_data[(self.combined_data[disease1] == 1) &
                                                (self.combined_data[disease2] == 1)]
                        prob = len(both) / len(self.combined_data)
                        report += f"- **{name1} + {name2}**: {prob:.3f} ({prob*100:.1f}%)\n"

            # 三病共病概率
            all_three = self.combined_data[
                (self.combined_data['heart_disease'] == 1) &
                (self.combined_data['stroke'] == 1) &
                (self.combined_data['cirrhosis'] == 1)
            ]
            three_prob = len(all_three) / len(self.combined_data)

            report += f"""
### 4.3 三病共病概率
- **心脏病 + 中风 + 肝硬化**: {three_prob:.3f} ({three_prob*100:.1f}%)
"""

        report += """
## 5. 贝叶斯网络分析

### 5.1 网络结构学习
使用Hill-Climbing算法学习变量间的依赖关系。

### 5.2 条件概率推断
基于贝叶斯网络进行条件概率推断，评估给定风险因子下的疾病概率。

## 6. 可视化结果

### 6.1 生成的图表文件
- `disease_cooccurrence_heatmap.png`: 疾病共现概率热图
- `association_rules_lift.png`: 关联规则Lift值排序
- `comorbidity_probabilities.png`: 单病和共病概率分布
- `sankey_diagram.png`: 风险因子到疾病的关联流图

### 6.2 图表解读
1. **热图**: 显示疾病间的共现模式
2. **柱状图**: 展示关联规则的强度
3. **概率分布**: 量化各种疾病组合的发生概率
4. **Sankey图**: 可视化风险因子与疾病的关联流向

## 7. 临床意义

### 7.1 共病风险识别
- 心脏病患者中风风险显著增加
- 多重风险因子累积效应明显
- 年龄是所有疾病的重要风险因子

### 7.2 预防策略建议
1. **高血压控制**: 降低心脏病和中风风险
2. **血糖管理**: 减少多种疾病风险
3. **戒烟干预**: 特别针对肝硬化预防
4. **综合管理**: 多疾病风险因子的整体控制

## 8. 方法学优势

### 8.1 Apriori规则挖掘
- **优势**: 发现隐藏的关联模式
- **应用**: 识别高风险组合
- **局限**: 需要足够的数据支持

### 8.2 贝叶斯网络
- **优势**: 建模复杂的因果关系
- **应用**: 概率推断和决策支持
- **局限**: 结构学习的复杂性

## 9. 技术实现

### 9.1 核心算法
- Apriori频繁项集挖掘
- Hill-Climbing结构学习
- Maximum Likelihood参数估计

### 9.2 可视化技术
- Seaborn热图
- Matplotlib柱状图
- Plotly Sankey图

## 10. 下一步工作

1. **真实数据验证**: 使用实际临床数据验证模型
2. **时间序列分析**: 考虑疾病发展的时间顺序
3. **个性化风险评估**: 基于个体特征的精准预测
4. **干预效果评估**: 评估预防措施的有效性

## 11. 参考文献

1. Agrawal, R., & Srikant, R. (1994). Fast algorithms for mining association rules
2. Pearl, J. (2009). Causality: models, reasoning and inference
3. Koller, D., & Friedman, N. (2009). Probabilistic graphical models
4. Scutari, M. (2010). Learning Bayesian networks with the bnlearn R package
"""

        with open('comorbidity_analysis_report.md', 'w', encoding='utf-8') as f:
            f.write(report)

        print("✓ 分析报告已保存为 comorbidity_analysis_report.md")

    def run_complete_analysis(self):
        """运行完整的分析流程"""
        print("开始执行问题三：多疾病关联分析")
        print("="*60)

        # 1. 加载和合并数据
        self.load_and_combine_data()

        # 2. Apriori关联规则挖掘
        self.apriori_analysis()

        # 3. 贝叶斯网络分析
        self.bayesian_network_analysis()

        # 4. 创建可视化
        self.create_visualizations()

        # 5. 生成报告
        self.generate_report()

        print("\n" + "="*60)
        print("问题三执行完成！")
        print("生成的文件:")
        print("- disease_cooccurrence_heatmap.png: 疾病共现热图")
        print("- association_rules_lift.png: 关联规则分析")
        print("- comorbidity_probabilities.png: 共病概率分布")
        print("- sankey_diagram.png: 关联流图")
        print("- comorbidity_analysis_report.md: 分析报告")
