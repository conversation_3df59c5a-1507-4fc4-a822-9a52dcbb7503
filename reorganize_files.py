"""
重新组织文件结构，将图片放到独立的文件夹中
"""
import os
import shutil

def reorganize_problem_files():
    """重新组织问题文件夹结构"""
    print("开始重新组织文件结构...")
    
    # 为每个问题创建图片文件夹
    for i in range(1, 5):
        problem_dir = f'problem{i}'
        images_dir = f'{problem_dir}/images'
        
        # 创建images文件夹
        os.makedirs(images_dir, exist_ok=True)
        print(f"✓ 创建文件夹: {images_dir}")
        
        # 查找并移动PNG文件
        if os.path.exists(problem_dir):
            for file in os.listdir(problem_dir):
                if file.endswith('.png'):
                    src_path = os.path.join(problem_dir, file)
                    dst_path = os.path.join(images_dir, file)
                    
                    # 移动文件
                    shutil.move(src_path, dst_path)
                    print(f"  移动: {file} -> {images_dir}/")
    
    print("\n文件重新组织完成！")
    
    # 显示新的文件结构
    print("\n新的文件结构:")
    for i in range(1, 5):
        problem_dir = f'problem{i}'
        images_dir = f'{problem_dir}/images'
        
        print(f"\n📁 {problem_dir}/")
        
        # 显示脚本文件
        if os.path.exists(problem_dir):
            for file in os.listdir(problem_dir):
                if file.endswith('.py') or file.endswith('.md') or file.endswith('.txt'):
                    print(f"  📄 {file}")
        
        # 显示images文件夹内容
        if os.path.exists(images_dir):
            images = [f for f in os.listdir(images_dir) if f.endswith('.png')]
            if images:
                print(f"  📁 images/")
                for img in images:
                    print(f"    🖼️ {img}")
        
        # 显示outputs文件夹内容
        outputs_dir = f'{problem_dir}/outputs'
        if os.path.exists(outputs_dir):
            outputs = [f for f in os.listdir(outputs_dir) if f.endswith('.md')]
            if outputs:
                print(f"  📁 outputs/")
                for output in outputs:
                    print(f"    📄 {output}")

if __name__ == "__main__":
    reorganize_problem_files()
