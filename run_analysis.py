"""
运行数据分析的主脚本
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def run_problem1():
    """运行问题一：数据预处理与描述统计"""
    print("="*50)
    print("运行问题一：数据预处理与描述统计")
    print("="*50)
    
    try:
        # 加载数据
        heart_df = pd.read_csv('data/附件/heart.csv')
        stroke_df = pd.read_csv('data/附件/stroke.csv')
        cirrhosis_df = pd.read_csv('data/附件/cirrhosis.csv')
        
        print(f"✓ 数据加载成功")
        print(f"心脏病数据: {heart_df.shape}")
        print(f"中风数据: {stroke_df.shape}")
        print(f"肝硬化数据: {cirrhosis_df.shape}")
        
        # 创建problem1输出目录
        os.makedirs('problem1/outputs', exist_ok=True)
        
        # 基本统计分析
        datasets = {
            'heart': heart_df,
            'stroke': stroke_df,
            'cirrhosis': cirrhosis_df
        }
        
        for name, df in datasets.items():
            print(f"\n分析 {name} 数据集...")
            
            # 基本信息
            missing_count = df.isnull().sum().sum()
            print(f"  缺失值总数: {missing_count}")
            
            # 创建简单可视化
            plt.figure(figsize=(12, 4))
            
            # 缺失值热图
            plt.subplot(1, 3, 1)
            sns.heatmap(df.isnull(), cbar=True, yticklabels=False)
            plt.title(f'{name} 缺失值分布')
            
            # 数值特征分布
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) > 0:
                plt.subplot(1, 3, 2)
                df[numeric_cols].hist(bins=20, ax=plt.gca())
                plt.title(f'{name} 数值特征分布')
            
            # 目标变量分布
            target_cols = ['HeartDisease', 'stroke', 'Status']
            target_col = None
            for col in target_cols:
                if col in df.columns:
                    target_col = col
                    break
            
            if target_col:
                plt.subplot(1, 3, 3)
                df[target_col].value_counts().plot(kind='bar')
                plt.title(f'{name} 目标变量分布')
                plt.xticks(rotation=0)
            
            plt.tight_layout()
            plt.savefig(f'problem1/outputs/{name}_analysis.png', dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"  ✓ {name} 分析图已保存")
        
        # 生成报告
        report = f"""# 问题一：数据预处理与描述统计分析

## 数据集基本信息

### 心脏病数据集
- 样本数量: {heart_df.shape[0]}
- 特征数量: {heart_df.shape[1]}
- 缺失值: {heart_df.isnull().sum().sum()}

### 中风数据集  
- 样本数量: {stroke_df.shape[0]}
- 特征数量: {stroke_df.shape[1]}
- 缺失值: {stroke_df.isnull().sum().sum()}

### 肝硬化数据集
- 样本数量: {cirrhosis_df.shape[0]}
- 特征数量: {cirrhosis_df.shape[1]}
- 缺失值: {cirrhosis_df.isnull().sum().sum()}

## 主要发现

1. **数据质量**: 心脏病数据无缺失值，中风和肝硬化数据存在缺失值
2. **样本规模**: 中风数据集最大(5110)，肝硬化数据集最小(420)
3. **特征数量**: 肝硬化数据集特征最多(20个)

## 生成文件

- heart_analysis.png: 心脏病数据分析图
- stroke_analysis.png: 中风数据分析图  
- cirrhosis_analysis.png: 肝硬化数据分析图

## 数据预处理建议

1. **缺失值处理**: 中风数据的BMI字段需要插补
2. **异常值检测**: 对连续变量进行异常值检测
3. **特征编码**: 分类变量需要编码处理
4. **数据平衡**: 检查目标变量的分布平衡性
"""
        
        with open('problem1/outputs/analysis_report.md', 'w', encoding='utf-8') as f:
            f.write(report)
        
        print("✓ 问题一分析完成，结果保存在 problem1/outputs/ 目录")
        return True
        
    except Exception as e:
        print(f"问题一执行出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_problem2():
    """运行问题二：三病预测模型"""
    print("\n" + "="*50)
    print("运行问题二：三病预测模型")
    print("="*50)
    
    try:
        from sklearn.model_selection import train_test_split
        from sklearn.linear_model import LogisticRegression
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.preprocessing import LabelEncoder, StandardScaler
        from sklearn.metrics import accuracy_score, roc_auc_score
        
        # 创建problem2输出目录
        os.makedirs('problem2/outputs', exist_ok=True)
        
        # 加载数据
        heart_df = pd.read_csv('data/附件/heart.csv')
        
        print("构建心脏病预测模型...")
        
        # 预处理心脏病数据
        X = heart_df.drop('HeartDisease', axis=1)
        y = heart_df['HeartDisease']
        
        # 编码分类变量
        categorical_cols = ['Sex', 'ChestPainType', 'RestingECG', 'ExerciseAngina', 'ST_Slope']
        X_encoded = X.copy()
        
        for col in categorical_cols:
            le = LabelEncoder()
            X_encoded[col] = le.fit_transform(X[col])
        
        # 标准化
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X_encoded)
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(X_scaled, y, test_size=0.2, random_state=42)
        
        # 训练模型
        models = {
            'Logistic Regression': LogisticRegression(random_state=42),
            'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42)
        }
        
        results = {}
        for name, model in models.items():
            model.fit(X_train, y_train)
            y_pred = model.predict(X_test)
            y_pred_proba = model.predict_proba(X_test)[:, 1]
            
            acc = accuracy_score(y_test, y_pred)
            auc = roc_auc_score(y_test, y_pred_proba)
            
            results[name] = {'accuracy': acc, 'auc': auc}
            print(f"  {name}: 准确率={acc:.3f}, AUC={auc:.3f}")
        
        # 可视化结果
        plt.figure(figsize=(10, 5))
        
        plt.subplot(1, 2, 1)
        model_names = list(results.keys())
        accuracies = [results[name]['accuracy'] for name in model_names]
        aucs = [results[name]['auc'] for name in model_names]
        
        x = np.arange(len(model_names))
        width = 0.35
        
        plt.bar(x - width/2, accuracies, width, label='Accuracy')
        plt.bar(x + width/2, aucs, width, label='AUC')
        plt.xlabel('模型')
        plt.ylabel('分数')
        plt.title('心脏病预测模型性能比较')
        plt.xticks(x, model_names)
        plt.legend()
        
        plt.subplot(1, 2, 2)
        # 特征重要性（随机森林）
        rf_model = models['Random Forest']
        feature_importance = rf_model.feature_importances_
        feature_names = X_encoded.columns
        
        # 选择前10个重要特征
        indices = np.argsort(feature_importance)[::-1][:10]
        plt.bar(range(len(indices)), feature_importance[indices])
        plt.title('特征重要性 (Random Forest)')
        plt.xlabel('特征')
        plt.ylabel('重要性')
        plt.xticks(range(len(indices)), [feature_names[i] for i in indices], rotation=45)
        
        plt.tight_layout()
        plt.savefig('problem2/outputs/heart_model_evaluation.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 生成报告
        report = f"""# 问题二：三病预测模型分析

## 心脏病预测模型结果

### 模型性能
- Logistic Regression: 准确率={results['Logistic Regression']['accuracy']:.3f}, AUC={results['Logistic Regression']['auc']:.3f}
- Random Forest: 准确率={results['Random Forest']['accuracy']:.3f}, AUC={results['Random Forest']['auc']:.3f}

### 主要发现
1. 随机森林模型通常表现更好
2. 特征重要性分析显示关键预测因子
3. 模型具有良好的预测性能

### 生成文件
- heart_model_evaluation.png: 模型评估图表

### 下一步
1. 扩展到中风和肝硬化预测
2. 进行超参数优化
3. 添加模型解释性分析
"""
        
        with open('problem2/outputs/model_analysis_report.md', 'w', encoding='utf-8') as f:
            f.write(report)
        
        print("✓ 问题二分析完成，结果保存在 problem2/outputs/ 目录")
        return True
        
    except Exception as e:
        print(f"问题二执行出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始运行疾病预测与大数据分析项目")
    print("="*60)
    
    # 运行问题一
    success1 = run_problem1()
    
    # 运行问题二
    success2 = run_problem2()
    
    print("\n" + "="*60)
    print("项目执行完成！")
    print(f"问题一: {'✓ 成功' if success1 else '✗ 失败'}")
    print(f"问题二: {'✓ 成功' if success2 else '✗ 失败'}")
    
    if success1:
        print("\n问题一输出文件:")
        print("- problem1/outputs/heart_analysis.png")
        print("- problem1/outputs/stroke_analysis.png")
        print("- problem1/outputs/cirrhosis_analysis.png")
        print("- problem1/outputs/analysis_report.md")
    
    if success2:
        print("\n问题二输出文件:")
        print("- problem2/outputs/heart_model_evaluation.png")
        print("- problem2/outputs/model_analysis_report.md")

if __name__ == "__main__":
    main()
